{"network_configuration": {"description": "UniFi Controller Configuration - Complete Switch Infrastructure", "controller_access": "VPN-only (blocked from WAN)", "management_vlan": 1, "switch_topology": "USW Flex 2.5G (core) + 24-Port Unmanaged + 4-Port PoE", "networks": [{"name": "Core Infrastructure & Wired", "vlan_id": 1, "subnet": "***********/24", "gateway": "***********", "dns_servers": ["***********13", "*******"], "dhcp_enabled": true, "dhcp_range": "************-************0", "purpose": "All wired devices, switches, NAS, UniFi devices", "security_level": "high", "device_types": "wired_only"}, {"name": "Guest Network", "vlan_id": 2, "subnet": "**********/24", "gateway": "**********", "dns_servers": ["*******", "*******"], "dhcp_enabled": true, "dhcp_range": "**********0-**********00", "purpose": "Guest WiFi internet access only", "security_level": "isolated", "bandwidth_limit": "50Mbps", "isolation": true, "device_types": "wifi_only"}, {"name": "Media Network", "vlan_id": 3, "subnet": "***********/24", "gateway": "192.168.3.1", "dns_servers": ["***********13", "*******"], "dhcp_enabled": true, "dhcp_range": "192.168.3.20-192.168.3.100", "purpose": "DLNA optimized - Yamaha speakers (wired) + WiFi clients", "security_level": "medium", "qos_priority": "high", "multicast_enabled": true, "device_types": "wired_and_wifi"}, {"name": "IoT Trusted", "vlan_id": 4, "subnet": "192.168.4.0/24", "gateway": "192.168.4.1", "dns_servers": ["***********13", "*******"], "dhcp_enabled": true, "dhcp_range": "192.168.4.20-192.168.4.100", "purpose": "Smart thermostat, air quality meter", "security_level": "medium", "bandwidth_limit": "10Mbps"}, {"name": "IoT Untrusted", "vlan_id": 6, "subnet": "192.168.6.0/24", "gateway": "192.168.6.1", "dns_servers": ["*******", "*******"], "dhcp_enabled": true, "dhcp_range": "192.168.6.10-192.168.6.200", "purpose": "Generic smart devices, cameras", "security_level": "low", "bandwidth_limit": "5Mbps", "internet_only": true}, {"name": "WiFi Users", "vlan_id": 10, "subnet": "192.168.10.0/24", "gateway": "192.168.10.1", "dns_servers": ["***********13", "*******"], "dhcp_enabled": true, "dhcp_range": "192.168.10.10-192.168.10.200", "purpose": "General WiFi users via AC Pro", "security_level": "high"}, {"name": "WiFi 6 Clients", "vlan_id": 11, "subnet": "192.168.11.0/24", "gateway": "192.168.11.1", "dns_servers": ["***********13", "*******"], "dhcp_enabled": true, "dhcp_range": "192.168.11.10-192.168.11.200", "purpose": "High-performance WiFi 6 clients via U6 Lite", "security_level": "high", "qos_priority": "high"}]}, "wifi_networks": [{"ssid": "HomeNetwork", "vlan_id": 10, "access_points": ["UniFi AC Pro"], "security": {"type": "WPA3", "fallback": "WPA2", "password": "CHANGE_THIS_PASSWORD"}, "settings": {"band_steering": true, "fast_roaming": true, "airtime_fairness": true, "broadcast_ssid": true}, "purpose": "Primary user network for general devices"}, {"ssid": "HomeNetwork-6", "vlan_id": 11, "access_points": ["UniFi U6 Lite"], "security": {"type": "WPA3", "fallback": "WPA2", "password": "CHANGE_THIS_PASSWORD"}, "settings": {"band_steering": true, "fast_roaming": true, "airtime_fairness": true, "broadcast_ssid": true, "wifi6_optimized": true}, "purpose": "High-performance network for WiFi 6 devices"}, {"ssid": "HomeGuest", "vlan_id": 2, "access_points": ["UniFi AC Pro", "UniFi U6 Lite"], "security": {"type": "WPA2", "password": "GUEST_PASSWORD"}, "settings": {"guest_portal": false, "bandwidth_limit": "50Mbps", "isolation": true, "broadcast_ssid": true}, "purpose": "Guest network with internet-only access"}, {"ssid": "Media-6", "vlan_id": 3, "access_points": ["UniFi U6 Lite"], "security": {"type": "WPA3", "fallback": "WPA2", "password": "MEDIA_PASSWORD"}, "settings": {"broadcast_ssid": false, "multicast_enhancement": true, "qos_priority": "high"}, "purpose": "Media streaming optimized network"}, {"ssid": "IoT-Trusted", "vlan_id": 4, "access_points": ["UniFi AC Pro"], "security": {"type": "WPA2", "password": "IOT_TRUSTED_PASSWORD"}, "settings": {"broadcast_ssid": false, "bandwidth_limit": "10Mbps", "client_isolation": true}, "purpose": "Trusted IoT devices (thermostat, air quality)"}, {"ssid": "IoT-Untrusted", "vlan_id": 6, "access_points": ["UniFi U6 Lite"], "security": {"type": "WPA2", "password": "IOT_UNTRUSTED_PASSWORD"}, "settings": {"broadcast_ssid": false, "bandwidth_limit": "5Mbps", "client_isolation": true, "internet_only": true}, "purpose": "Generic smart devices with internet-only access"}], "access_point_configuration": {"UniFi AC Pro": {"ip_address": "***********", "location": "Main living areas", "primary_networks": ["HomeNetwork", "HomeGuest", "IoT-Trusted"], "settings": {"channel_width_2g": "20MHz", "channel_width_5g": "80MHz", "transmit_power": "auto", "band_steering": true, "fast_roaming": true}, "coverage_optimization": "balanced"}, "UniFi U6 Lite": {"ip_address": "***********", "location": "High-performance areas", "primary_networks": ["HomeNetwork-6", "Media-6", "IoT-Untrusted", "HomeGuest"], "settings": {"channel_width_2g": "20MHz", "channel_width_5g": "80MHz", "channel_width_6g": "80MHz", "transmit_power": "auto", "band_steering": true, "fast_roaming": true, "wifi6_features": true}, "coverage_optimization": "performance"}}, "switch_configuration": {"USW Flex 2.5G": {"ip_address": "***********", "port_profiles": [{"port": 1, "speed": "2.5G", "description": "Synology NAS - High Performance", "vlan": "all", "poe": false, "purpose": "Maximum throughput for NAS"}, {"port": 2, "speed": "1G", "description": "UniFi AC Pro - Trunk", "vlan": "all", "poe": true, "purpose": "Power and data for AC Pro"}, {"port": 3, "speed": "1G", "description": "UniFi U6 Lite - Trunk", "vlan": "all", "poe": true, "purpose": "Power and data for U6 Lite"}, {"port": 4, "speed": "1G", "description": "Wired Clients", "vlan": 1, "poe": false, "purpose": "Standard wired devices"}, {"port": 5, "speed": "2.5G", "description": "Future Expansion", "vlan": "all", "poe": false, "purpose": "Reserved for high-performance device"}], "vlan_configuration": {"native_vlan": 1, "allowed_vlans": [1, 2, 3, 4, 6, 10, 11], "trunk_ports": [2, 3, 5], "access_ports": [1, 4]}}}, "security_settings": {"wireless_security": {"wpa3_enabled": true, "wpa2_fallback": true, "mac_randomization": "handled", "rogue_ap_detection": true, "wireless_intrusion_detection": true}, "network_security": {"inter_vlan_routing": "controlled", "guest_isolation": true, "iot_isolation": true, "management_vlan_protection": true}, "access_control": {"controller_access": "vpn_only", "device_management": "vlan_1_only", "guest_portal": false, "captive_portal": false}}, "qos_configuration": {"traffic_priorities": [{"name": "Media Streaming", "priority": 1, "vlans": [3], "protocols": ["DLNA", "UPnP", "HTTP streaming"], "bandwidth_guarantee": "40%"}, {"name": "Interactive Services", "priority": 2, "vlans": [1, 5, 10, 11], "protocols": ["HTTP", "HTTPS", "SSH", "VPN"], "bandwidth_guarantee": "30%"}, {"name": "IoT Communication", "priority": 3, "vlans": [4], "protocols": ["MQTT", "HTTP", "CoAP"], "bandwidth_guarantee": "10%"}, {"name": "Guest and Untrusted", "priority": 4, "vlans": [2, 6], "protocols": ["HTTP", "HTTPS"], "bandwidth_guarantee": "20%"}]}, "monitoring_and_analytics": {"traffic_analysis": {"dpi_enabled": true, "application_identification": true, "bandwidth_monitoring": true, "per_vlan_statistics": true}, "security_monitoring": {"anomaly_detection": true, "iot_behavior_analysis": true, "rogue_device_detection": true, "intrusion_detection": true}, "performance_monitoring": {"channel_utilization": true, "client_experience": true, "ap_performance": true, "switch_performance": true}}, "implementation_notes": {"controller_setup": ["Install UniFi Controller on Synology NAS", "Configure for VPN-only access", "Set up all network profiles before adopting devices", "Adopt devices in correct order: switch, then APs"], "device_adoption": ["Adopt USW Flex 2.5G switch first", "Configure port profiles and VLANs", "Adopt UniFi AC Pro and assign to correct location", "Adopt UniFi U6 Lite and configure WiFi 6 settings"], "testing_procedures": ["Test VLAN isolation between networks", "Verify 2.5G connection to NAS", "Test WiFi performance on both APs", "Validate guest network isolation", "Confirm IoT device segmentation"], "optimization_tips": ["Use WiFi 6 AP for high-bandwidth clients", "Separate IoT devices by trust level", "Monitor channel utilization and adjust as needed", "Regularly update firmware on all devices"]}}