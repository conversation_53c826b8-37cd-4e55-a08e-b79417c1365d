#!/bin/vbash
# DS923+ Dual NIC Network Topology Configuration
# Optimized configuration leveraging Synology DS923+ dual network ports
# 
# Hardware:
# - EdgeRouter 4: Primary gateway and VPN server
# - USW Flex 2.5G: Core managed switch (VLAN routing)
# - Synology DS923+: Dual 1G NICs configured in Link Aggregation (LAG)
# - 24-Port Unmanaged Switch: Access layer for wired devices
# - 4-Port PoE Switch: Dedicated for UniFi access points
# - UniFi AC Pro & U6 Lite: WiFi access points

# Source the Vyatta environment
source /opt/vyatta/etc/functions/script-template

echo "=== DS923+ Dual NIC Network Topology Configuration ==="
echo "Configuring Link Aggregation (LAG) for maximum performance and redundancy"
echo ""
echo "WARNING: This script will execute EdgeRouter configuration commands directly!"
echo "Make sure you have a backup of your current configuration."
echo ""
echo -n "Continue with configuration? (y/N): "
read REPLY
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Configuration cancelled."
    exit 0
fi
echo ""

# Enter configuration mode
configure

echo "1. Configure VLAN Structure:"
echo ""

echo "# Keep eth1 with existing IP and add DHCP (Core Infrastructure)"
set interfaces ethernet eth1 description 'LAN - Core Infrastructure & Trunk to USW Flex 2.5G'
set service dhcp-server shared-network-name LAN subnet ***********/24 default-router ***********
set service dhcp-server shared-network-name LAN subnet ***********/24 dns-server ***********13
set service dhcp-server shared-network-name LAN subnet ***********/24 dns-server *******
set service dhcp-server shared-network-name LAN subnet ***********/24 lease 86400
set service dhcp-server shared-network-name LAN subnet ***********/24 start ************ stop ************0
echo "✓ Core network (eth1) configured with DHCP"

echo "# 2"
set interfaces ethernet eth1 vif 2 address **********/24
set interfaces ethernet eth1 vif 2 description 'Guest Network - WiFi Only'
echo "✓ VLAN 2 configured"

echo "# VLAN 3 - Media Network (***********/24) - Wired + WiFi"
set interfaces ethernet eth1 vif 3 address ***********/24
set interfaces ethernet eth1 vif 3 description 'Media Network - DLNA Optimized'
echo "✓ VLAN 3 configured"

echo "# VLAN 4 - IoT Trusted (***********/24) - WiFi Only"
set interfaces ethernet eth1 vif 4 address ***********/24
set interfaces ethernet eth1 vif 4 description 'IoT Trusted - WiFi Only'
echo "✓ VLAN 4 configured"

echo "# VLAN 6 - IoT Untrusted (***********/24) - WiFi Only"
set interfaces ethernet eth1 vif 6 address ***********/24
set interfaces ethernet eth1 vif 6 description 'IoT Untrusted - WiFi Only'
echo "✓ VLAN 6 configured"

echo "# VLAN 10 - Primary WiFi Users (************/24)"
set interfaces ethernet eth1 vif 10 address ************/24
set interfaces ethernet eth1 vif 10 description 'Primary WiFi Users'
echo "✓ VLAN 10 configured"
echo ""

echo "2. Configure DHCP for remaining network segments:"
echo ""

echo "# Static reservations for infrastructure devices (UPDATE MAC ADDRESSES!)"
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping usw-flex ip-address ***********
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping usw-flex mac-address 94:2a:6f:4c:fb:58
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping unifi-ac-pro ip-address ***********
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping unifi-ac-pro mac-address 68:d7:9a:d6:25:46
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping unifi-u6-lite ip-address ***********
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping unifi-u6-lite mac-address 70:a7:41:c6:e7:f8
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping synology-nas-lag ip-address ***********13
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping synology-nas-lag mac-address 60:32:b1:5d:54:4b
echo "✓ Static reservations configured (MAC addresses commented - update manually)"

echo "# DHCP for Guest Network (VLAN 2) - WiFi Only"
set service dhcp-server shared-network-name GUEST subnet **********/24 default-router **********
set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******
set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******
set service dhcp-server shared-network-name GUEST subnet **********/24 lease 3600
set service dhcp-server shared-network-name GUEST subnet **********/24 start **********0 stop **********00
echo "✓ Guest DHCP configured"

echo "# DHCP for Media Network (VLAN 3) - Wired + WiFi"
set service dhcp-server shared-network-name MEDIA subnet ***********/24 default-router ***********
set service dhcp-server shared-network-name MEDIA subnet ***********/24 dns-server ***********13
set service dhcp-server shared-network-name MEDIA subnet ***********/24 dns-server *******
set service dhcp-server shared-network-name MEDIA subnet ***********/24 lease 86400
set service dhcp-server shared-network-name MEDIA subnet ***********/24 start ************ stop ***********00
echo "✓ Media DHCP configured"

echo "# Static reservations for Yamaha speakers (UPDATE MAC ADDRESSES!)"
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker ip-address ***********0
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker mac-address 00:A0:DE:DB:55:08
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker ip-address ***********1
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker mac-address 00:A0:DE:F6:B1:37
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker ip-address ***********2
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker mac-address 54:b7:bd:c6:a4:ad
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker ip-address ***********3
set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker mac-address 88:4a:ea:30:0a:0d
echo "✓ Speaker reservations configured (MAC addresses need to be added manually)"

echo "# DHCP for IoT networks"
set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 default-router ***********
set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 dns-server ***********13
set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 lease 86400
set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 start ***********0 stop ************

set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 default-router ***********
set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 dns-server *******
set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 lease 43200
set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 start ***********0 stop *************
echo "✓ IoT DHCP configured"

echo "# DHCP for Primary WiFi Users (VLAN 10)"
set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 default-router ************
set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 dns-server ***********13
set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 dns-server *******
set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 lease 86400
set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 start ************0 stop **************
echo "✓ WiFi Users DHCP configured"
echo ""

echo "3. Create network groups:"
echo ""

set firewall group network-group WIRED_DEVICES network ***********/24
set firewall group network-group WIRED_DEVICES description 'All Wired Devices - Trusted'
set firewall group network-group WIFI_USERS network ************/24
set firewall group network-group WIFI_USERS description 'Primary WiFi Users'
set firewall group network-group MEDIA_NETWORKS network ***********/24
set firewall group network-group MEDIA_NETWORKS description 'Media Network - Wired + WiFi'
set firewall group network-group IOT_TRUSTED network ***********/24
set firewall group network-group IOT_TRUSTED description 'IoT Trusted - WiFi Only'
set firewall group network-group IOT_UNTRUSTED network ***********/24
set firewall group network-group IOT_UNTRUSTED description 'IoT Untrusted - WiFi Only'
set firewall group network-group GUEST_NETWORK network **********/24
set firewall group network-group GUEST_NETWORK description 'Guest Network - WiFi Only'
set firewall group network-group VPN_NETWORK network ***********/24
set firewall group network-group VPN_NETWORK description 'VPN Clients'
set firewall group network-group TRUSTED_NETWORKS network ***********/24
set firewall group network-group TRUSTED_NETWORKS network ***********/24
set firewall group network-group TRUSTED_NETWORKS network ***********/24
set firewall group network-group TRUSTED_NETWORKS network ************/24
set firewall group network-group TRUSTED_NETWORKS description 'All Trusted Networks'
echo "✓ Network groups configured"
echo ""

echo "4. Configure multicast support for media network:"
echo ""

set protocols igmp-proxy interface eth1 role upstream
set protocols igmp-proxy interface eth1.3 role downstream
set protocols igmp-proxy interface wg0 role downstream
set service mdns repeater interface eth1
set service mdns repeater interface eth1.3
set service mdns repeater interface wg0
echo "✓ Multicast and mDNS configured"
echo ""

echo "5. Configure NAT for internet access:"
echo ""

set service nat rule 5000 description 'Core/wired devices to internet'
set service nat rule 5000 outbound-interface eth0
set service nat rule 5000 source address ***********/24
set service nat rule 5000 type masquerade

set service nat rule 5010 description 'WiFi users to internet'
set service nat rule 5010 outbound-interface eth0
set service nat rule 5010 source address ************/24
set service nat rule 5010 type masquerade

set service nat rule 5020 description 'Media network to internet'
set service nat rule 5020 outbound-interface eth0
set service nat rule 5020 source address ***********/24
set service nat rule 5020 type masquerade

set service nat rule 5030 description 'IoT trusted to internet'
set service nat rule 5030 outbound-interface eth0
set service nat rule 5030 source address ***********/24
set service nat rule 5030 type masquerade

set service nat rule 5040 description 'IoT untrusted to internet'
set service nat rule 5040 outbound-interface eth0
set service nat rule 5040 source address ***********/24
set service nat rule 5040 type masquerade

set service nat rule 5050 description 'Guest network to internet'
set service nat rule 5050 outbound-interface eth0
set service nat rule 5050 source address **********/24
set service nat rule 5050 type masquerade

set service nat rule 5060 description 'VPN clients to internet'
set service nat rule 5060 outbound-interface eth0
set service nat rule 5060 source address ***********/24
set service nat rule 5060 type masquerade
echo "✓ NAT rules configured"
echo ""

echo "6. Commit and save configuration:"
echo ""
commit
save
echo "✓ Configuration committed and saved"
echo ""

echo "=== DS923+ Dual NIC Network Topology Configuration Complete ==="
echo ""
echo "Network Topology with DS923+ Link Aggregation:"
echo "- EdgeRouter 4 → USW Flex 2.5G (trunk all VLANs)"
echo "- USW Flex Port 2 → DS923+ Port 1 (LAG Member 1)"
echo "- USW Flex Port 3 → DS923+ Port 2 (LAG Member 2)"
echo "- DS923+ LAG Interface: 2 Gbps aggregate bandwidth"
echo "- USW Flex Port 4 → 24-Port Switch (wired devices)"
echo "- USW Flex Port 5 → 4-Port PoE Switch (UniFi APs)"
echo "- USW Flex Port 1: 2.5G available for future expansion"
echo ""
echo "IMPORTANT: Remember to also configure:"
echo "1. DS923+ Link Aggregation in DSM (Control Panel > Network > Network Interface)"
echo "2. USW Flex LAG port profile for ports 2-3"
echo "3. Update MAC addresses for static reservations (commented lines)"
echo "4. Test connectivity and performance after both are configured"
