# DS923+ Dual NIC Network Topology Configuration
# Optimized configuration leveraging Synology DS923+ dual network ports
# 
# Hardware:
# - EdgeRouter 4: Primary gateway and VPN server
# - USW Flex 2.5G: Core managed switch (VLAN routing)
# - Synology DS923+: Dual 1G NICs configured in Link Aggregation (LAG)
# - 24-Port Unmanaged Switch: Access layer for wired devices
# - 4-Port PoE Switch: Dedicated for UniFi access points
# - UniFi AC Pro & U6 Lite: WiFi access points

echo "=== DS923+ Dual NIC Network Topology Configuration ==="
echo "Configuring Link Aggregation (LAG) for maximum performance and redundancy"
echo ""

# ===== VLAN CONFIGURATION (Same as before) =====

echo "1. Configure VLAN Structure (unchanged):"
echo ""

echo "# VLAN 1 - Core Infrastructure & All Wired Devices (***********/24)"
echo "set interfaces ethernet eth1 description 'LAN - Trunk to USW Flex 2.5G'"
echo "set interfaces ethernet eth1 address ***********/24"
echo ""

echo "# VLAN 2 - Guest Network (**********/24) - WiFi Only"
echo "set interfaces ethernet eth1 vif 2 address **********/24"
echo "set interfaces ethernet eth1 vif 2 description 'Guest Network - WiFi Only'"
echo ""

echo "# VLAN 3 - Media Network (***********/24) - Wired + WiFi"
echo "set interfaces ethernet eth1 vif 3 address ***********/24"
echo "set interfaces ethernet eth1 vif 3 description 'Media Network - DLNA Optimized'"
echo ""

echo "# VLAN 4 - IoT Trusted (***********/24) - WiFi Only"
echo "set interfaces ethernet eth1 vif 4 address ***********/24"
echo "set interfaces ethernet eth1 vif 4 description 'IoT Trusted - WiFi Only'"
echo ""

echo "# VLAN 6 - IoT Untrusted (***********/24) - WiFi Only"
echo "set interfaces ethernet eth1 vif 6 address ***********/24"
echo "set interfaces ethernet eth1 vif 6 description 'IoT Untrusted - WiFi Only'"
echo ""

echo "# VLAN 10 - Primary WiFi Users (************/24)"
echo "set interfaces ethernet eth1 vif 10 address ************/24"
echo "set interfaces ethernet eth1 vif 10 description 'Primary WiFi Users'"
echo ""

# ===== DHCP CONFIGURATION =====

echo "2. Configure DHCP for all network segments:"
echo ""

echo "# DHCP for Core Infrastructure & Wired Devices (VLAN 1)"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 default-router ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 dns-server ***********13"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 dns-server *******"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 lease 86400"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 start ************ stop ************0"
echo ""

echo "# Static reservations for infrastructure devices"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping usw-flex ip-address ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping usw-flex mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping unifi-ac-pro ip-address ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping unifi-ac-pro mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping unifi-u6-lite ip-address ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping unifi-u6-lite mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping 24p-switch ip-address ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping 24p-switch mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping 4p-poe-switch ip-address ***********"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping 4p-poe-switch mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "# DS923+ LAG Interface - Single IP for bonded interface"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping synology-nas-lag ip-address ***********13"
echo "set service dhcp-server shared-network-name CORE subnet ***********/24 static-mapping synology-nas-lag mac-address XX:XX:XX:XX:XX:XX"  # LAG MAC
echo ""

echo "# DHCP for Guest Network (VLAN 2) - WiFi Only"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 default-router **********"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 lease 3600"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 start **********0 stop **********00"
echo ""

echo "# DHCP for Media Network (VLAN 3) - Wired + WiFi"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 default-router ***********"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 dns-server ***********13"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 dns-server *******"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 lease 86400"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 start ************ stop ***********00"
echo ""

echo "# Static reservations for Yamaha speakers (wired via 24-port switch)"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker ip-address ***********0"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker ip-address ***********1"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker ip-address ***********2"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker mac-address XX:XX:XX:XX:XX:XX"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker ip-address ***********3"
echo "set service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "# DHCP for IoT Trusted (VLAN 4) - WiFi Only"
echo "set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 default-router ***********"
echo "set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 dns-server ***********13"
echo "set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 lease 86400"
echo "set service dhcp-server shared-network-name IOT_TRUSTED subnet ***********/24 start ***********0 stop ************"
echo ""

echo "# DHCP for IoT Untrusted (VLAN 6) - WiFi Only"
echo "set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 default-router ***********"
echo "set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 dns-server *******"
echo "set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 lease 43200"
echo "set service dhcp-server shared-network-name IOT_UNTRUSTED subnet ***********/24 start ***********0 stop ***********00"
echo ""

echo "# DHCP for Primary WiFi Users (VLAN 10)"
echo "set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 default-router ************"
echo "set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 dns-server ***********13"
echo "set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 dns-server *******"
echo "set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 lease 86400"
echo "set service dhcp-server shared-network-name WIFI_USERS subnet ************/24 start ************0 stop **************"
echo ""

# ===== NETWORK GROUPS =====

echo "3. Create network groups:"
echo ""

echo "# Core infrastructure (all wired devices)"
echo "set firewall group network-group WIRED_DEVICES network ***********/24"
echo "set firewall group network-group WIRED_DEVICES description 'All Wired Devices - Trusted'"
echo ""

echo "# WiFi user networks"
echo "set firewall group network-group WIFI_USERS network ************/24"
echo "set firewall group network-group WIFI_USERS description 'Primary WiFi Users'"
echo ""

echo "# Media networks"
echo "set firewall group network-group MEDIA_NETWORKS network ***********/24"
echo "set firewall group network-group MEDIA_NETWORKS description 'Media Network - Wired + WiFi'"
echo ""

echo "# IoT networks"
echo "set firewall group network-group IOT_TRUSTED network ***********/24"
echo "set firewall group network-group IOT_TRUSTED description 'IoT Trusted - WiFi Only'"
echo ""

echo "set firewall group network-group IOT_UNTRUSTED network ***********/24"
echo "set firewall group network-group IOT_UNTRUSTED description 'IoT Untrusted - WiFi Only'"
echo ""

echo "# Guest network"
echo "set firewall group network-group GUEST_NETWORK network **********/24"
echo "set firewall group network-group GUEST_NETWORK description 'Guest Network - WiFi Only'"
echo ""

echo "# VPN network"
echo "set firewall group network-group VPN_NETWORK network ***********/24"
echo "set firewall group network-group VPN_NETWORK description 'VPN Clients'"
echo ""

echo "# All trusted internal networks"
echo "set firewall group network-group TRUSTED_NETWORKS network ***********/24"
echo "set firewall group network-group TRUSTED_NETWORKS network ***********/24"
echo "set firewall group network-group TRUSTED_NETWORKS network ***********/24"
echo "set firewall group network-group TRUSTED_NETWORKS network ************/24"
echo "set firewall group network-group TRUSTED_NETWORKS description 'All Trusted Networks'"
echo ""

# ===== MULTICAST SUPPORT FOR DLNA =====

echo "4. Configure multicast support for media network:"
echo ""

echo "# Enable IGMP proxy for DLNA (media network)"
echo "set protocols igmp-proxy interface eth1 role upstream"
echo "set protocols igmp-proxy interface eth1.3 role downstream"  # Media network
echo "set protocols igmp-proxy interface wg0 role downstream"     # VPN
echo ""

echo "# Enable mDNS reflection for service discovery"
echo "set service mdns repeater interface eth1"      # Core network
echo "set service mdns repeater interface eth1.3"    # Media network
echo "set service mdns repeater interface wg0"       # VPN
echo ""

# ===== NAT CONFIGURATION =====

echo "5. Configure NAT for internet access:"
echo ""

echo "# NAT rules for all networks (unchanged)"
echo "set service nat rule 100 description 'Core/wired devices to internet'"
echo "set service nat rule 100 outbound-interface eth0"
echo "set service nat rule 100 source address ***********/24"
echo "set service nat rule 100 type masquerade"
echo ""

echo "set service nat rule 110 description 'WiFi users to internet'"
echo "set service nat rule 110 outbound-interface eth0"
echo "set service nat rule 110 source address ************/24"
echo "set service nat rule 110 type masquerade"
echo ""

echo "set service nat rule 120 description 'Media network to internet'"
echo "set service nat rule 120 outbound-interface eth0"
echo "set service nat rule 120 source address ***********/24"
echo "set service nat rule 120 type masquerade"
echo ""

echo "set service nat rule 130 description 'IoT trusted to internet'"
echo "set service nat rule 130 outbound-interface eth0"
echo "set service nat rule 130 source address ***********/24"
echo "set service nat rule 130 type masquerade"
echo ""

echo "set service nat rule 140 description 'IoT untrusted to internet'"
echo "set service nat rule 140 outbound-interface eth0"
echo "set service nat rule 140 source address ***********/24"
echo "set service nat rule 140 type masquerade"
echo ""

echo "set service nat rule 150 description 'Guest network to internet'"
echo "set service nat rule 150 outbound-interface eth0"
echo "set service nat rule 150 source address **********/24"
echo "set service nat rule 150 type masquerade"
echo ""

echo "set service nat rule 160 description 'VPN clients to internet'"
echo "set service nat rule 160 outbound-interface eth0"
echo "set service nat rule 160 source address ***********/24"
echo "set service nat rule 160 type masquerade"
echo ""

echo "=== DS923+ Dual NIC Network Topology Configuration Complete ==="
echo ""
echo "Network Topology with DS923+ Link Aggregation:"
echo "- EdgeRouter 4 → USW Flex 2.5G (trunk all VLANs)"
echo "- USW Flex Port 2 → DS923+ Port 1 (LAG Member 1)"
echo "- USW Flex Port 3 → DS923+ Port 2 (LAG Member 2)"
echo "- DS923+ LAG Interface: 2 Gbps aggregate bandwidth"
echo "- USW Flex Port 4 → 24-Port Switch (wired devices)"
echo "- USW Flex Port 5 → 4-Port PoE Switch (UniFi APs)"
echo "- USW Flex Port 1: 2.5G available for future expansion"
echo ""
echo "Performance Benefits:"
echo "- NAS Bandwidth: 2 Gbps aggregate (100% improvement)"
echo "- Redundancy: Automatic failover protection"
echo "- Load Balancing: Traffic distributed across both links"
echo "- Future Expansion: 2.5G port reserved for next-gen devices"
echo ""
echo "Next Steps:"
echo "1. Configure DS923+ Link Aggregation in DSM"
echo "2. Configure USW Flex LAG port profile"
echo "3. Test connectivity and performance"
echo "4. Monitor LAG status and traffic distribution"
