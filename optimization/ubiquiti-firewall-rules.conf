# Ubiquiti Infrastructure Firewall Rules
# Enhanced firewall configuration for EdgeRouter 4 with UniFi infrastructure
# 
# Network Topology:
# - VLAN 1 (192.168.1.0/24): Core Infrastructure
# - VLAN 2 (172.16.1.0/24): Guest Network (ISOLATED)
# - VLAN 3 (192.168.3.0/24): Media Network (DLNA)
# - VLAN 4 (192.168.4.0/24): IoT Trusted
# - VLAN 5 (192.168.5.0/24): VPN Network
# - VLAN 6 (192.168.6.0/24): IoT Untrusted
# - VLAN 10 (************/24): WiFi Users (AC Pro)
# - VLAN 11 (************/24): WiFi 6 Clients (U6 Lite)

echo "=== Ubiquiti Infrastructure Firewall Rules ==="
echo "Configuring enhanced firewall rules for complete Ubiquiti infrastructure"
echo ""

# ===== INTER-VLAN FIREWALL RULES =====

echo "1. Configure Inter-VLAN Access Rules:"
echo ""

echo "# WiFi Users (VLAN 10) to Core Infrastructure"
echo "set firewall name WIFI_USERS_IN default-action drop"
echo "set firewall name WIFI_USERS_IN description 'WiFi users to internal'"
echo ""

echo "# Allow WiFi users to access core services"
echo "set firewall name WIFI_USERS_IN rule 10 action accept"
echo "set firewall name WIFI_USERS_IN rule 10 description 'Allow WiFi users to core services'"
echo "set firewall name WIFI_USERS_IN rule 10 destination group network-group CORE_INFRASTRUCTURE"
echo "set firewall name WIFI_USERS_IN rule 10 destination port 80,443,8080,8443,9080,3000,5000,5001,8123"  !!!!
echo "set firewall name WIFI_USERS_IN rule 10 protocol tcp"
echo ""

echo "# Allow WiFi users to access media network"
echo "set firewall name WIFI_USERS_IN rule 20 action accept"
echo "set firewall name WIFI_USERS_IN rule 20 description 'Allow WiFi users to media network'"
echo "set firewall name WIFI_USERS_IN rule 20 destination group network-group MEDIA_NETWORKS"
echo "set firewall name WIFI_USERS_IN rule 20 protocol tcp_udp"
echo ""

echo "# Allow established and related connections"
echo "set firewall name WIFI_USERS_IN rule 30 action accept"
echo "set firewall name WIFI_USERS_IN rule 30 description 'Allow established/related'"
echo "set firewall name WIFI_USERS_IN rule 30 state established enable"
echo "set firewall name WIFI_USERS_IN rule 30 state related enable"
echo ""

echo "# Allow DNS queries"
echo "set firewall name WIFI_USERS_IN rule 40 action accept"
echo "set firewall name WIFI_USERS_IN rule 40 description 'Allow DNS queries'"
echo "set firewall name WIFI_USERS_IN rule 40 destination port 53"
echo "set firewall name WIFI_USERS_IN rule 40 protocol tcp_udp"
echo ""

echo "# Block access to UniFi devices (security)"
echo "set firewall name WIFI_USERS_IN rule 50 action drop"
echo "set firewall name WIFI_USERS_IN rule 50 description 'Block direct access to UniFi devices'"
echo "set firewall name WIFI_USERS_IN rule 50 destination group address-group UNIFI_DEVICES"
echo "set firewall name WIFI_USERS_IN rule 50 destination port 22,80,443,8080,8443"
echo "set firewall name WIFI_USERS_IN rule 50 protocol tcp"
echo "set firewall name WIFI_USERS_IN rule 50 log enable"
echo ""

echo "# Apply WiFi users firewall"
echo "set interfaces ethernet eth1 vif 10 firewall in name WIFI_USERS_IN"
echo ""

echo "# WiFi 6 Clients (VLAN 11) - Similar rules with high performance"  !!!!!!
echo "set firewall name WIFI6_CLIENTS_IN default-action drop"
echo "set firewall name WIFI6_CLIENTS_IN description 'WiFi 6 clients to internal'"
echo ""

echo "# Allow WiFi 6 clients to access all internal services (high-performance users)"
echo "set firewall name WIFI6_CLIENTS_IN rule 10 action accept"
echo "set firewall name WIFI6_CLIENTS_IN rule 10 description 'Allow WiFi 6 clients to core services'"
echo "set firewall name WIFI6_CLIENTS_IN rule 10 destination group network-group CORE_INFRASTRUCTURE"
echo "set firewall name WIFI6_CLIENTS_IN rule 10 protocol tcp_udp"
echo ""

echo "set firewall name WIFI6_CLIENTS_IN rule 20 action accept"
echo "set firewall name WIFI6_CLIENTS_IN rule 20 description 'Allow WiFi 6 clients to media network'"
echo "set firewall name WIFI6_CLIENTS_IN rule 20 destination group network-group MEDIA_NETWORKS"
echo "set firewall name WIFI6_CLIENTS_IN rule 20 protocol tcp_udp"
echo ""

echo "set firewall name WIFI6_CLIENTS_IN rule 30 action accept"
echo "set firewall name WIFI6_CLIENTS_IN rule 30 description 'Allow established/related'"
echo "set firewall name WIFI6_CLIENTS_IN rule 30 state established enable"
echo "set firewall name WIFI6_CLIENTS_IN rule 30 state related enable"
echo ""

echo "set firewall name WIFI6_CLIENTS_IN rule 40 action accept"
echo "set firewall name WIFI6_CLIENTS_IN rule 40 description 'Allow DNS queries'"
echo "set firewall name WIFI6_CLIENTS_IN rule 40 destination port 53"
echo "set firewall name WIFI6_CLIENTS_IN rule 40 protocol tcp_udp"
echo ""

echo "# Block access to UniFi devices (security)"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 action drop"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 description 'Block direct access to UniFi devices'"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 destination group address-group UNIFI_DEVICES"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 destination port 22,80,443,8080,8443"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 protocol tcp"
echo "set firewall name WIFI6_CLIENTS_IN rule 50 log enable"
echo ""

echo "# Apply WiFi 6 clients firewall"
echo "set interfaces ethernet eth1 vif 11 firewall in name WIFI6_CLIENTS_IN"
echo ""

# ===== MEDIA NETWORK FIREWALL RULES =====

echo "2. Configure Media Network Firewall Rules:"
echo ""

echo "set firewall name MEDIA_IN default-action drop"
echo "set firewall name MEDIA_IN description 'Media network access control'"
echo ""

echo "# Allow DLNA/UPnP multicast traffic"
echo "set firewall name MEDIA_IN rule 10 action accept"
echo "set firewall name MEDIA_IN rule 10 description 'Allow DLNA multicast discovery'"
echo "set firewall name MEDIA_IN rule 10 destination address ***************"
echo "set firewall name MEDIA_IN rule 10 destination port 1900"
echo "set firewall name MEDIA_IN rule 10 protocol udp"
echo ""

echo "# Allow media streaming to Synology NAS"
echo "set firewall name MEDIA_IN rule 20 action accept"
echo "set firewall name MEDIA_IN rule 20 description 'Allow media streaming to NAS'"
echo "set firewall name MEDIA_IN rule 20 destination address *************"
echo "set firewall name MEDIA_IN rule 20 destination port 8096,5000,5001,80,443"
echo "set firewall name MEDIA_IN rule 20 protocol tcp"
echo ""

echo "# Allow established and related connections"
echo "set firewall name MEDIA_IN rule 30 action accept"
echo "set firewall name MEDIA_IN rule 30 description 'Allow established/related'"
echo "set firewall name MEDIA_IN rule 30 state established enable"
echo "set firewall name MEDIA_IN rule 30 state related enable"
echo ""

echo "# Allow DNS queries"
echo "set firewall name MEDIA_IN rule 40 action accept"
echo "set firewall name MEDIA_IN rule 40 description 'Allow DNS queries'"
echo "set firewall name MEDIA_IN rule 40 destination port 53"
echo "set firewall name MEDIA_IN rule 40 protocol tcp_udp"
echo ""

echo "# Block access to other internal networks"
echo "set firewall name MEDIA_IN rule 50 action drop"
echo "set firewall name MEDIA_IN rule 50 description 'Block access to other networks'"
echo "set firewall name MEDIA_IN rule 50 destination group network-group USER_NETWORKS"
echo "set firewall name MEDIA_IN rule 50 log enable"
echo ""

echo "# Apply media network firewall"
echo "set interfaces ethernet eth1 vif 3 firewall in name MEDIA_IN"
echo ""

# ===== IOT TRUSTED FIREWALL RULES =====>>>>>>>

echo "3. Configure IoT Trusted Network Firewall Rules:"
echo ""

echo "set firewall name IOT_TRUSTED_IN default-action drop"
echo "set firewall name IOT_TRUSTED_IN description 'IoT trusted devices access control'"
echo ""

echo "# Allow IoT devices to communicate with Home Assistant"
echo "set firewall name IOT_TRUSTED_IN rule 10 action accept"
echo "set firewall name IOT_TRUSTED_IN rule 10 description 'Allow IoT to Home Assistant'"
echo "set firewall name IOT_TRUSTED_IN rule 10 destination address *************"
echo "set firewall name IOT_TRUSTED_IN rule 10 destination port 8123"
echo "set firewall name IOT_TRUSTED_IN rule 10 protocol tcp"
echo ""

echo "# Allow IoT devices to access Zigbee controller"
echo "set firewall name IOT_TRUSTED_IN rule 20 action accept"
echo "set firewall name IOT_TRUSTED_IN rule 20 description 'Allow IoT to Zigbee controller'"
echo "set firewall name IOT_TRUSTED_IN rule 20 destination address *************"
echo "set firewall name IOT_TRUSTED_IN rule 20 destination port 8080"
echo "set firewall name IOT_TRUSTED_IN rule 20 protocol tcp"
echo ""

echo "# Allow MQTT communication"
echo "set firewall name IOT_TRUSTED_IN rule 30 action accept"
echo "set firewall name IOT_TRUSTED_IN rule 30 description 'Allow MQTT communication'"
echo "set firewall name IOT_TRUSTED_IN rule 30 destination address *************"
echo "set firewall name IOT_TRUSTED_IN rule 30 destination port 1883,8883"
echo "set firewall name IOT_TRUSTED_IN rule 30 protocol tcp"
echo ""

echo "# Allow NTP and DNS"
echo "set firewall name IOT_TRUSTED_IN rule 40 action accept"
echo "set firewall name IOT_TRUSTED_IN rule 40 description 'Allow NTP and DNS'"
echo "set firewall name IOT_TRUSTED_IN rule 40 destination port 53,123"
echo "set firewall name IOT_TRUSTED_IN rule 40 protocol tcp_udp"
echo ""

echo "# Allow established and related connections"
echo "set firewall name IOT_TRUSTED_IN rule 50 action accept"
echo "set firewall name IOT_TRUSTED_IN rule 50 description 'Allow established/related'"
echo "set firewall name IOT_TRUSTED_IN rule 50 state established enable"
echo "set firewall name IOT_TRUSTED_IN rule 50 state related enable"
echo ""

echo "# Block access to all other internal networks"
echo "set firewall name IOT_TRUSTED_IN rule 60 action drop"
echo "set firewall name IOT_TRUSTED_IN rule 60 description 'Block access to other networks'"
echo "set firewall name IOT_TRUSTED_IN rule 60 destination group network-group ALL_INTERNAL"
echo "set firewall name IOT_TRUSTED_IN rule 60 log enable"
echo ""

echo "# Block access to UniFi devices"
echo "set firewall name IOT_TRUSTED_IN rule 70 action drop"
echo "set firewall name IOT_TRUSTED_IN rule 70 description 'Block access to UniFi devices'"
echo "set firewall name IOT_TRUSTED_IN rule 70 destination group address-group UNIFI_DEVICES"
echo "set firewall name IOT_TRUSTED_IN rule 70 log enable"
echo ""

echo "# Apply IoT trusted firewall"
echo "set interfaces ethernet eth1 vif 4 firewall in name IOT_TRUSTED_IN"
echo ""

# ===== IOT UNTRUSTED FIREWALL RULES =====

echo "4. Configure IoT Untrusted Network Firewall Rules:"
echo ""

echo "set firewall name IOT_UNTRUSTED_IN default-action drop"
echo "set firewall name IOT_UNTRUSTED_IN description 'IoT untrusted - internet only'"
echo ""

echo "# Allow DNS queries"
echo "set firewall name IOT_UNTRUSTED_IN rule 10 action accept"
echo "set firewall name IOT_UNTRUSTED_IN rule 10 description 'Allow DNS queries'"
echo "set firewall name IOT_UNTRUSTED_IN rule 10 destination port 53"
echo "set firewall name IOT_UNTRUSTED_IN rule 10 protocol tcp_udp"
echo ""

echo "# Allow NTP for time synchronization"
echo "set firewall name IOT_UNTRUSTED_IN rule 20 action accept"
echo "set firewall name IOT_UNTRUSTED_IN rule 20 description 'Allow NTP for time sync'"
echo "set firewall name IOT_UNTRUSTED_IN rule 20 destination port 123"
echo "set firewall name IOT_UNTRUSTED_IN rule 20 protocol udp"
echo ""

echo "# Allow established and related connections"
echo "set firewall name IOT_UNTRUSTED_IN rule 30 action accept"
echo "set firewall name IOT_UNTRUSTED_IN rule 30 description 'Allow established/related'"
echo "set firewall name IOT_UNTRUSTED_IN rule 30 state established enable"
echo "set firewall name IOT_UNTRUSTED_IN rule 30 state related enable"
echo ""

echo "# Block ALL access to internal networks"
echo "set firewall name IOT_UNTRUSTED_IN rule 40 action drop"
echo "set firewall name IOT_UNTRUSTED_IN rule 40 description 'Block ALL internal network access'"
echo "set firewall name IOT_UNTRUSTED_IN rule 40 destination group network-group ALL_INTERNAL"   !!!!!
echo "set firewall name IOT_UNTRUSTED_IN rule 40 log enable"
echo ""

echo "# Block access to UniFi devices"
echo "set firewall name IOT_UNTRUSTED_IN rule 50 action drop"
echo "set firewall name IOT_UNTRUSTED_IN rule 50 description 'Block access to UniFi devices'"
echo "set firewall name IOT_UNTRUSTED_IN rule 50 destination group address-group UNIFI_DEVICES"
echo "set firewall name IOT_UNTRUSTED_IN rule 50 log enable"
echo ""

echo "# Apply IoT untrusted firewall"
echo "set interfaces ethernet eth1 vif 6 firewall in name IOT_UNTRUSTED_IN"
echo ""

# ===== GUEST NETWORK FIREWALL RULES =====

echo "5. Configure Guest Network Firewall Rules:"
echo ""

echo "set firewall name GUEST_IN default-action drop"
echo "set firewall name GUEST_IN description 'Guest network - internet only'"
echo ""

echo "# Allow DNS queries"
echo "set firewall name GUEST_IN rule 10 action accept"
echo "set firewall name GUEST_IN rule 10 description 'Allow DNS queries'"
echo "set firewall name GUEST_IN rule 10 destination port 53"
echo "set firewall name GUEST_IN rule 10 protocol tcp_udp"
echo ""

echo "# Allow established and related connections"
echo "set firewall name GUEST_IN rule 20 action accept"
echo "set firewall name GUEST_IN rule 20 description 'Allow established/related'"
echo "set firewall name GUEST_IN rule 20 state established enable"
echo "set firewall name GUEST_IN rule 20 state related enable"
echo ""

echo "# Block ALL access to internal networks"
echo "set firewall name GUEST_IN rule 30 action drop"
echo "set firewall name GUEST_IN rule 30 description 'Block ALL internal network access'"
echo "set firewall name GUEST_IN rule 30 destination group network-group ALL_INTERNAL"     !!!!!
echo "set firewall name GUEST_IN rule 30 log enable"
echo ""

echo "# Block access to UniFi devices"
echo "set firewall name GUEST_IN rule 40 action drop"
echo "set firewall name GUEST_IN rule 40 description 'Block access to UniFi devices'"
echo "set firewall name GUEST_IN rule 40 destination group address-group UNIFI_DEVICES"
echo "set firewall name GUEST_IN rule 40 log enable"
echo ""

echo "# Block access to EdgeRouter management"
echo "set firewall name GUEST_IN rule 50 action drop"
echo "set firewall name GUEST_IN rule 50 description 'Block EdgeRouter management'"
echo "set firewall name GUEST_IN rule 50 destination address **********"
echo "set firewall name GUEST_IN rule 50 destination port 22,80,443"
echo "set firewall name GUEST_IN rule 50 protocol tcp"
echo "set firewall name GUEST_IN rule 50 log enable"
echo ""

echo "# Apply guest network firewall"
echo "set interfaces ethernet eth1 vif 2 firewall in name GUEST_IN"
echo ""

# ===== VPN ACCESS RULES =====

echo "6. Configure VPN Access Rules:"
echo ""

echo "set firewall name VPN_IN default-action accept"
echo "set firewall name VPN_IN description 'VPN clients to internal networks'"
echo ""

echo "# Allow VPN clients to access all internal networks"
echo "set firewall name VPN_IN rule 10 action accept"
echo "set firewall name VPN_IN rule 10 description 'Allow VPN to all internal networks'"
echo "set firewall name VPN_IN rule 10 source group network-group VPN_NETWORK"
echo "set firewall name VPN_IN rule 10 destination group network-group ALL_INTERNAL"
echo ""

echo "# Allow VPN clients to manage UniFi devices"  !!!!!
echo "set firewall name VPN_IN rule 20 action accept"
echo "set firewall name VPN_IN rule 20 description 'Allow VPN to manage UniFi devices'"
echo "set firewall name VPN_IN rule 20 source group network-group VPN_NETWORK"
echo "set firewall name VPN_IN rule 20 destination group address-group UNIFI_DEVICES"
echo ""

echo "# Apply VPN firewall"
echo "set interfaces wireguard wg0 firewall in name VPN_IN"
echo ""

# ===== WAN BLOCKING RULES =====

echo "7. Configure WAN Blocking Rules (Updated for all services):"
echo ""

echo "set firewall name WAN_IN default-action drop"
echo "set firewall name WAN_IN description 'WAN to internal - block all services except VPN'"
echo ""

echo "# Allow WireGuard VPN"
echo "set firewall name WAN_IN rule 10 action accept"
echo "set firewall name WAN_IN rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_IN rule 10 destination port 51820"
echo "set firewall name WAN_IN rule 10 protocol udp"
echo ""

echo "# Block all service ports from WAN"
echo "set firewall name WAN_IN rule 20 action drop"
echo "set firewall name WAN_IN rule 20 description 'Block all internal service ports'"
echo "set firewall name WAN_IN rule 20 destination port 80,443,8080,8443,9080,3000,5000,5001,8123,8096,9000,8880,8081,8082"
echo "set firewall name WAN_IN rule 20 protocol tcp"
echo "set firewall name WAN_IN rule 20 log enable"
echo ""

echo "# Block UniFi device management from WAN"
echo "set firewall name WAN_IN rule 30 action drop"
echo "set firewall name WAN_IN rule 30 description 'Block UniFi device access from WAN'"
echo "set firewall name WAN_IN rule 30 destination group address-group UNIFI_DEVICES"
echo "set firewall name WAN_IN rule 30 protocol tcp_udp"
echo "set firewall name WAN_IN rule 30 log enable"
echo ""

echo "# Apply WAN firewall"
echo "set interfaces ethernet eth0 firewall in name WAN_IN"
echo ""

echo "=== Ubiquiti Infrastructure Firewall Rules Complete ==="
echo ""
echo "Firewall Rules Applied:"
echo "- WiFi Users (VLAN 10): Access to core services, blocked from UniFi devices"
echo "- WiFi 6 Clients (VLAN 11): Full access to internal services, blocked from UniFi devices"
echo "- Media Network (VLAN 3): DLNA optimized, access to media services only"
echo "- IoT Trusted (VLAN 4): Home Assistant and Zigbee access only"
echo "- IoT Untrusted (VLAN 6): Internet only, no internal access"
echo "- Guest Network (VLAN 2): Internet only, complete isolation"
echo "- VPN Network (VLAN 5): Full access to all networks and management"
echo "- WAN: All services blocked except VPN (port 51820)"
echo ""
echo "Security Features:"
echo "- UniFi devices protected from untrusted networks"
echo "- Inter-VLAN communication controlled"
echo "- Comprehensive logging for security events"
echo "- VPN-only access to management interfaces"
