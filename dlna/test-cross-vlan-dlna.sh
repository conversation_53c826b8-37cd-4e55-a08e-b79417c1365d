#!/bin/bash
# Cross-VLAN DLNA Connectivity Test Script
# Tests DLNA/UPnP discovery and connectivity between Jellyfin and Yamaha speakers

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
JELLYFIN_SERVER="*************"
JELLYFIN_HTTP_PORT="8096"
JELLYFIN_DLNA_PORT="8920"

YAMAHA_SPEAKERS=(
    "************:livingroom"
    "************:kitchen"
    "************:bureau"
    "************:bedroom"
)

MULTICAST_SSDP="***************"
MULTICAST_MDNS="***********"
SSDP_PORT="1900"
MDNS_PORT="5353"

echo -e "${BLUE}=== Cross-VLAN DLNA Connectivity Test ===${NC}"
echo "Testing DLNA/UPnP connectivity between networks"
echo "Date: $(date)"
echo ""

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} - $test_name"
    elif [ "$result" = "FAIL" ]; then
        echo -e "${RED}✗ FAIL${NC} - $test_name"
    elif [ "$result" = "WARN" ]; then
        echo -e "${YELLOW}⚠ WARN${NC} - $test_name"
    else
        echo -e "${BLUE}ℹ INFO${NC} - $test_name"
    fi
    
    if [ -n "$details" ]; then
        echo "  Details: $details"
    fi
    echo ""
}

# Function to test basic network connectivity
test_network_connectivity() {
    echo -e "${BLUE}=== Network Connectivity Tests ===${NC}"
    
    # Test Jellyfin server
    if ping -c 3 -W 2 "$JELLYFIN_SERVER" >/dev/null 2>&1; then
        print_result "Jellyfin Server Connectivity" "PASS" "$JELLYFIN_SERVER is reachable"
    else
        print_result "Jellyfin Server Connectivity" "FAIL" "$JELLYFIN_SERVER is not reachable"
    fi
    
    # Test Yamaha speakers
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if ping -c 3 -W 2 "$ip" >/dev/null 2>&1; then
            print_result "Speaker Connectivity ($name)" "PASS" "$ip is reachable"
        else
            print_result "Speaker Connectivity ($name)" "FAIL" "$ip is not reachable"
        fi
    done
}

# Function to test HTTP service accessibility
test_http_services() {
    echo -e "${BLUE}=== HTTP Service Tests ===${NC}"
    
    # Test Jellyfin HTTP
    if curl -s -I "http://$JELLYFIN_SERVER:$JELLYFIN_HTTP_PORT" | head -1 | grep -q "200\|302"; then
        print_result "Jellyfin HTTP Service" "PASS" "Port $JELLYFIN_HTTP_PORT accessible"
    else
        print_result "Jellyfin HTTP Service" "FAIL" "Port $JELLYFIN_HTTP_PORT not accessible"
    fi
    
    # Test Jellyfin DLNA
    if timeout 5 bash -c "</dev/tcp/$JELLYFIN_SERVER/$JELLYFIN_DLNA_PORT" 2>/dev/null; then
        print_result "Jellyfin DLNA Service" "PASS" "Port $JELLYFIN_DLNA_PORT accessible"
    else
        print_result "Jellyfin DLNA Service" "FAIL" "Port $JELLYFIN_DLNA_PORT not accessible"
    fi
    
    # Test Yamaha speaker HTTP interfaces
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if curl -s -I "http://$ip" | head -1 | grep -q "200\|302\|404"; then
            print_result "Speaker HTTP ($name)" "PASS" "$ip web interface accessible"
        else
            print_result "Speaker HTTP ($name)" "FAIL" "$ip web interface not accessible"
        fi
    done
}

# Function to test multicast connectivity
test_multicast() {
    echo -e "${BLUE}=== Multicast Tests ===${NC}"
    
    # Check if multicast routing is enabled
    if [ -f /proc/sys/net/ipv4/ip_forward ] && [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
        print_result "IP Forwarding" "PASS" "Multicast routing enabled"
    else
        print_result "IP Forwarding" "WARN" "IP forwarding may not be enabled"
    fi
    
    # Test SSDP multicast group membership
    if ip maddr show | grep -q "$MULTICAST_SSDP"; then
        print_result "SSDP Multicast Group" "PASS" "Device is member of SSDP group"
    else
        print_result "SSDP Multicast Group" "WARN" "Device is not member of SSDP group"
    fi
    
    # Test mDNS multicast group membership
    if ip maddr show | grep -q "$MULTICAST_MDNS"; then
        print_result "mDNS Multicast Group" "PASS" "Device is member of mDNS group"
    else
        print_result "mDNS Multicast Group" "WARN" "Device is not member of mDNS group"
    fi
}

# Function to test SSDP discovery
test_ssdp_discovery() {
    echo -e "${BLUE}=== SSDP Discovery Tests ===${NC}"
    
    if command -v nc >/dev/null 2>&1; then
        echo "Sending SSDP M-SEARCH request..."
        
        # Create SSDP discovery message
        ssdp_message="M-SEARCH * HTTP/1.1\r\nHOST: $MULTICAST_SSDP:$SSDP_PORT\r\nMAN: \"ssdp:discover\"\r\nST: upnp:rootdevice\r\nMX: 3\r\n\r\n"
        
        # Send SSDP discovery and capture responses
        response=$(echo -e "$ssdp_message" | timeout 5 nc -u "$MULTICAST_SSDP" "$SSDP_PORT" 2>/dev/null)
        
        if [ -n "$response" ]; then
            device_count=$(echo "$response" | grep -c "HTTP/1.1 200 OK" || echo "0")
            print_result "SSDP Discovery" "PASS" "Received $device_count SSDP responses"
            
            # Show discovered devices
            if [ "$device_count" -gt 0 ]; then
                echo "  Discovered devices:"
                echo "$response" | grep -E "(SERVER|LOCATION)" | head -10
                echo ""
            fi
        else
            print_result "SSDP Discovery" "FAIL" "No SSDP responses received"
        fi
    else
        print_result "SSDP Discovery" "WARN" "netcat not available for SSDP testing"
    fi
}

# Function to test mDNS discovery
test_mdns_discovery() {
    echo -e "${BLUE}=== mDNS Discovery Tests ===${NC}"
    
    if command -v avahi-browse >/dev/null 2>&1; then
        echo "Scanning for mDNS services (10 second timeout)..."
        services=$(timeout 10 avahi-browse -t _services._dns-sd._udp.local 2>/dev/null | wc -l)
        if [ "$services" -gt 0 ]; then
            print_result "mDNS Service Discovery" "PASS" "Found $services mDNS services"
        else
            print_result "mDNS Service Discovery" "WARN" "No mDNS services found"
        fi
        
        # Look for specific DLNA services
        echo "Scanning for DLNA services..."
        dlna_services=$(timeout 10 avahi-browse -t _upnp._tcp.local 2>/dev/null | wc -l)
        if [ "$dlna_services" -gt 0 ]; then
            print_result "DLNA mDNS Services" "PASS" "Found $dlna_services DLNA services"
        else
            print_result "DLNA mDNS Services" "WARN" "No DLNA services found via mDNS"
        fi
    else
        print_result "mDNS Tools" "WARN" "avahi-browse not available for mDNS testing"
    fi
}

# Function to test Jellyfin DLNA API
test_jellyfin_dlna() {
    echo -e "${BLUE}=== Jellyfin DLNA Tests ===${NC}"
    
    # Test Jellyfin DLNA device list
    if command -v curl >/dev/null 2>&1; then
        dlna_devices=$(curl -s "http://$JELLYFIN_SERVER:$JELLYFIN_HTTP_PORT/dlna" 2>/dev/null)
        
        if [ -n "$dlna_devices" ]; then
            print_result "Jellyfin DLNA API" "PASS" "DLNA API accessible"
            
            # Check for Yamaha devices in the response
            yamaha_count=$(echo "$dlna_devices" | grep -ci "yamaha\|musiccast" || echo "0")
            if [ "$yamaha_count" -gt 0 ]; then
                print_result "Yamaha Device Detection" "PASS" "Found $yamaha_count Yamaha devices in Jellyfin"
            else
                print_result "Yamaha Device Detection" "FAIL" "No Yamaha devices found in Jellyfin"
            fi
        else
            print_result "Jellyfin DLNA API" "FAIL" "DLNA API not accessible"
        fi
    else
        print_result "Jellyfin DLNA API" "WARN" "curl not available for API testing"
    fi
}

# Function to test EdgeRouter IGMP configuration
test_edgerouter_config() {
    echo -e "${BLUE}=== EdgeRouter Configuration Tests ===${NC}"
    
    # These tests would need to be run on the EdgeRouter itself
    print_result "EdgeRouter Tests" "INFO" "Run these commands on EdgeRouter:"
    echo "  show protocols igmp-proxy"
    echo "  show service mdns repeater"
    echo "  show ip igmp groups"
    echo "  show firewall name LAN_IN | grep -A5 -B5 1900"
    echo ""
}

# Function to generate recommendations
generate_recommendations() {
    echo -e "${BLUE}=== Troubleshooting Recommendations ===${NC}"
    
    echo "Based on test results, try these solutions:"
    echo ""
    
    echo "1. If basic connectivity fails:"
    echo "   - Check VLAN configuration and routing"
    echo "   - Verify firewall rules allow inter-VLAN traffic"
    echo "   - Test with firewall temporarily disabled"
    echo ""
    
    echo "2. If HTTP services fail:"
    echo "   - Check Jellyfin service status"
    echo "   - Verify Synology firewall settings"
    echo "   - Restart Jellyfin container/service"
    echo ""
    
    echo "3. If multicast discovery fails:"
    echo "   - Apply EdgeRouter IGMP proxy configuration"
    echo "   - Enable mDNS repeater on EdgeRouter"
    echo "   - Check for multicast filtering on switches"
    echo ""
    
    echo "4. If SSDP discovery fails:"
    echo "   - Verify UDP port 1900 is open"
    echo "   - Check multicast routing between VLANs"
    echo "   - Restart network services"
    echo ""
    
    echo "5. If Yamaha devices not detected:"
    echo "   - Enable DLNA renderer on speakers"
    echo "   - Check speaker network standby settings"
    echo "   - Power cycle speakers"
    echo "   - Update speaker firmware"
    echo ""
    
    echo "6. Configuration files to apply:"
    echo "   - dlna/cross-vlan-dlna-configuration.sh (EdgeRouter)"
    echo "   - dlna/jellyfin-dlna-setup.md (Jellyfin settings)"
    echo ""
}

# Main execution
main() {
    echo "Starting comprehensive DLNA connectivity tests..."
    echo "This may take several minutes to complete."
    echo ""
    
    test_network_connectivity
    test_http_services
    test_multicast
    test_ssdp_discovery
    test_mdns_discovery
    test_jellyfin_dlna
    test_edgerouter_config
    generate_recommendations
    
    echo -e "${GREEN}=== DLNA Connectivity Test Complete ===${NC}"
    echo "Review the results above and apply recommended configurations."
    echo ""
}

# Check if running with appropriate permissions
if [ "$EUID" -ne 0 ]; then
    echo -e "${YELLOW}Note: Some tests may require root privileges for full functionality${NC}"
    echo ""
fi

# Run main function
main
