#!/bin/bash
# Cross-VLAN DLNA/UPnP Configuration for Jellyfin-Yamaha Integration
# Enables DLNA discovery and control between:
# - Jellyfin Server: VLAN 1 (*************)
# - Yamaha Speakers: VLAN 3 (***********/24)
# - Client Devices: VLAN 10 (************/24)

echo "=== Cross-VLAN DLNA/UPnP Configuration ==="
echo "Configuring EdgeRouter 4 for Jellyfin-Yamaha DLNA integration"
echo "Date: $(date)"
echo ""

echo "Network Configuration:"
echo "- Jellyfin Server: ************* (VLAN 1)"
echo "- Yamaha Speakers: ***********/24 (VLAN 3)"
echo "- Client Devices: ************/24 (VLAN 10)"
echo "- EdgeRouter: 192.168.1.1"
echo ""

echo "IMPORTANT: Run these commands on your EdgeRouter 4 via SSH"
echo ""

echo "1. Configure Enhanced IGMP Proxy for Multicast Routing:"
echo "configure"
echo ""

echo "# Remove existing IGMP proxy configuration"
echo "delete protocols igmp-proxy"
echo ""

echo "# Configure IGMP proxy for cross-VLAN multicast"
echo "set protocols igmp-proxy interface eth1 role upstream"
echo "set protocols igmp-proxy interface eth1 alt-subnet ***********/24"
echo "set protocols igmp-proxy interface eth1 threshold 1"
echo ""

echo "# Media Network (VLAN 3) - Yamaha Speakers"
echo "set protocols igmp-proxy interface eth1.3 role downstream"
echo "set protocols igmp-proxy interface eth1.3 alt-subnet ***********/24"
echo "set protocols igmp-proxy interface eth1.3 threshold 1"
echo ""

echo "# WiFi Users (VLAN 10) - Client Devices"
echo "set protocols igmp-proxy interface eth1.10 role downstream"
echo "set protocols igmp-proxy interface eth1.10 alt-subnet ************/24"
echo "set protocols igmp-proxy interface eth1.10 threshold 1"
echo ""

echo "# VPN Network (VLAN 5) - Remote Access"
echo "set protocols igmp-proxy interface wg0 role downstream"
echo "set protocols igmp-proxy interface wg0 alt-subnet ***********/24"
echo "set protocols igmp-proxy interface wg0 threshold 1"
echo ""

echo "2. Configure Enhanced mDNS Repeater for Service Discovery:"
echo ""

echo "# Remove existing mDNS configuration"
echo "delete service mdns repeater"
echo ""

echo "# Configure mDNS repeater for cross-VLAN discovery"
echo "set service mdns repeater interface eth1"        # Core network (VLAN 1)
echo "set service mdns repeater interface eth1.3"      # Media network (VLAN 3)
echo "set service mdns repeater interface eth1.10"     # WiFi users (VLAN 10)
echo "set service mdns repeater interface wg0"         # VPN network (VLAN 5)"
echo ""

echo "# Enable specific service discovery domains"
echo "set service mdns repeater browse-domain local"
echo "set service mdns repeater browse-domain _http._tcp.local"
echo "set service mdns repeater browse-domain _upnp._tcp.local"
echo "set service mdns repeater browse-domain _dlna._tcp.local"
echo "set service mdns repeater browse-domain _musiccast._tcp.local"
echo "set service mdns repeater browse-domain _yamaha-mc._tcp.local"
echo ""

echo "3. Create Network Groups for DLNA Configuration:"
echo ""

echo "# Create network groups for easier firewall management"
echo "set firewall group network-group JELLYFIN_SERVER network *************/32"
echo "set firewall group network-group JELLYFIN_SERVER description 'Jellyfin Media Server'"
echo ""

echo "set firewall group network-group YAMAHA_SPEAKERS network ***********/24"
echo "set firewall group network-group YAMAHA_SPEAKERS description 'Yamaha MusicCast Speakers'"
echo ""

echo "set firewall group network-group DLNA_CLIENTS network ***********/24"
echo "set firewall group network-group DLNA_CLIENTS network ************/24"
echo "set firewall group network-group DLNA_CLIENTS network ***********/24"
echo "set firewall group network-group DLNA_CLIENTS description 'DLNA Client Networks'"
echo ""

echo "# Create port groups for DLNA/UPnP services"
echo "set firewall group port-group DLNA_DISCOVERY_PORTS port 1900"
echo "set firewall group port-group DLNA_DISCOVERY_PORTS port 5353"
echo "set firewall group port-group DLNA_DISCOVERY_PORTS description 'DLNA Discovery Ports'"
echo ""

echo "set firewall group port-group DLNA_CONTROL_PORTS port 8096"    # Jellyfin HTTP
echo "set firewall group port-group DLNA_CONTROL_PORTS port 8920"    # Jellyfin DLNA
echo "set firewall group port-group DLNA_CONTROL_PORTS port 1900"    # UPnP
echo "set firewall group port-group DLNA_CONTROL_PORTS port 7359"    # Jellyfin discovery
echo "set firewall group port-group DLNA_CONTROL_PORTS description 'DLNA Control Ports'"
echo ""

echo "set firewall group port-group YAMAHA_PORTS port 80"
echo "set firewall group port-group YAMAHA_PORTS port 443"
echo "set firewall group port-group YAMAHA_PORTS port 5005"
echo "set firewall group port-group YAMAHA_PORTS port 8080"
echo "set firewall group port-group YAMAHA_PORTS port 49154"
echo "set firewall group port-group YAMAHA_PORTS description 'Yamaha MusicCast Ports'"
echo ""

echo "4. Configure DLNA-Specific Firewall Rules:"
echo ""

echo "# Allow DLNA discovery from all client networks to Jellyfin server"
echo "set firewall name LAN_IN rule 100 action accept"
echo "set firewall name LAN_IN rule 100 description 'Allow DLNA discovery to Jellyfin'"
echo "set firewall name LAN_IN rule 100 source group network-group DLNA_CLIENTS"
echo "set firewall name LAN_IN rule 100 destination group network-group JELLYFIN_SERVER"
echo "set firewall name LAN_IN rule 100 destination group port-group DLNA_CONTROL_PORTS"
echo "set firewall name LAN_IN rule 100 protocol tcp_udp"
echo ""

echo "# Allow DLNA discovery from all client networks to Yamaha speakers"
echo "set firewall name LAN_IN rule 101 action accept"
echo "set firewall name LAN_IN rule 101 description 'Allow DLNA discovery to Yamaha speakers'"
echo "set firewall name LAN_IN rule 101 source group network-group DLNA_CLIENTS"
echo "set firewall name LAN_IN rule 101 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name LAN_IN rule 101 destination group port-group YAMAHA_PORTS"
echo "set firewall name LAN_IN rule 101 protocol tcp_udp"
echo ""

echo "# Allow multicast SSDP discovery traffic"
echo "set firewall name LAN_IN rule 102 action accept"
echo "set firewall name LAN_IN rule 102 description 'Allow SSDP multicast discovery'"
echo "set firewall name LAN_IN rule 102 destination address ***************"
echo "set firewall name LAN_IN rule 102 destination port 1900"
echo "set firewall name LAN_IN rule 102 protocol udp"
echo ""

echo "# Allow mDNS multicast traffic"
echo "set firewall name LAN_IN rule 103 action accept"
echo "set firewall name LAN_IN rule 103 description 'Allow mDNS multicast discovery'"
echo "set firewall name LAN_IN rule 103 destination address ***********"
echo "set firewall name LAN_IN rule 103 destination port 5353"
echo "set firewall name LAN_IN rule 103 protocol udp"
echo ""

echo "5. Configure Media Network (VLAN 3) Specific Rules:"
echo ""

echo "# Update MEDIA_IN firewall to allow DLNA traffic"
echo "set firewall name MEDIA_IN rule 50 action accept"
echo "set firewall name MEDIA_IN rule 50 description 'Allow DLNA from Jellyfin server'"
echo "set firewall name MEDIA_IN rule 50 source group network-group JELLYFIN_SERVER"
echo "set firewall name MEDIA_IN rule 50 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name MEDIA_IN rule 50 destination group port-group YAMAHA_PORTS"
echo "set firewall name MEDIA_IN rule 50 protocol tcp_udp"
echo ""

echo "set firewall name MEDIA_IN rule 51 action accept"
echo "set firewall name MEDIA_IN rule 51 description 'Allow DLNA from client networks'"
echo "set firewall name MEDIA_IN rule 51 source group network-group DLNA_CLIENTS"
echo "set firewall name MEDIA_IN rule 51 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name MEDIA_IN rule 51 destination group port-group YAMAHA_PORTS"
echo "set firewall name MEDIA_IN rule 51 protocol tcp_udp"
echo ""

echo "6. Configure WiFi Users (VLAN 10) Rules:"
echo ""

echo "# Allow WiFi users to access Jellyfin server for DLNA"
echo "set firewall name WIFI_USERS_IN rule 100 action accept"
echo "set firewall name WIFI_USERS_IN rule 100 description 'Allow DLNA access to Jellyfin'"
echo "set firewall name WIFI_USERS_IN rule 100 destination group network-group JELLYFIN_SERVER"
echo "set firewall name WIFI_USERS_IN rule 100 destination group port-group DLNA_CONTROL_PORTS"
echo "set firewall name WIFI_USERS_IN rule 100 protocol tcp_udp"
echo ""

echo "# Allow WiFi users to control Yamaha speakers"
echo "set firewall name WIFI_USERS_IN rule 101 action accept"
echo "set firewall name WIFI_USERS_IN rule 101 description 'Allow control of Yamaha speakers'"
echo "set firewall name WIFI_USERS_IN rule 101 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name WIFI_USERS_IN rule 101 destination group port-group YAMAHA_PORTS"
echo "set firewall name WIFI_USERS_IN rule 101 protocol tcp_udp"
echo ""

echo "7. Enable Multicast Routing:"
echo ""

echo "# Enable IP multicast routing"
echo "set protocols static multicast-route ***************/32 next-hop-interface eth1"
echo "set protocols static multicast-route ***************/32 next-hop-interface eth1.3"
echo "set protocols static multicast-route ***************/32 next-hop-interface eth1.10"
echo ""

echo "set protocols static multicast-route ***********/32 next-hop-interface eth1"
echo "set protocols static multicast-route ***********/32 next-hop-interface eth1.3"
echo "set protocols static multicast-route ***********/32 next-hop-interface eth1.10"
echo ""

echo "8. Commit and Save Configuration:"
echo ""
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Verification Commands ==="
echo ""

echo "1. Check IGMP proxy status:"
echo "show protocols igmp-proxy"
echo "show protocols igmp-proxy interface"
echo ""

echo "2. Check mDNS repeater status:"
echo "show service mdns repeater"
echo ""

echo "3. Check multicast group membership:"
echo "show ip igmp groups"
echo ""

echo "4. Test multicast connectivity:"
echo "# From VLAN 10 client, test SSDP discovery"
echo "echo 'M-SEARCH * HTTP/1.1' | nc -u *************** 1900"
echo ""

echo "5. Monitor DLNA traffic:"
echo "sudo tcpdump -i any port 1900 or port 5353 or port 8096"
echo ""

echo "=== Jellyfin Configuration ==="
echo ""
echo "On your Jellyfin server (*************), ensure:"
echo "1. DLNA is enabled in Jellyfin settings"
echo "2. Network discovery is enabled"
echo "3. Firewall allows ports 8096, 8920, 1900, 7359"
echo "4. Jellyfin is bound to all interfaces (0.0.0.0)"
echo ""

echo "=== Yamaha Speaker Configuration ==="
echo ""
echo "On your Yamaha speakers:"
echo "1. Ensure network standby is enabled"
echo "2. DLNA/Network services are enabled"
echo "3. Speakers are on the correct VLAN 3 network"
echo "4. Firmware is up to date"
echo ""

echo "=== Testing Procedure ==="
echo ""
echo "1. Restart Jellyfin service on Synology NAS"
echo "2. Restart Yamaha speakers (power cycle)"
echo "3. Wait 2-3 minutes for discovery"
echo "4. Check Jellyfin web interface for detected DLNA devices"
echo "5. Test audio casting from Jellyfin client"
echo ""

echo "=== Troubleshooting ==="
echo ""
echo "If DLNA discovery still fails:"
echo "1. Check firewall logs: show log tail 50 | grep -i drop"
echo "2. Monitor multicast traffic: sudo tcpdump -i any multicast"
echo "3. Verify IGMP membership: show ip igmp groups"
echo "4. Test direct IP access to speakers from Jellyfin server"
echo "5. Check Jellyfin logs for DLNA errors"
