# Jellyfin DLNA Configuration for Cross-VLAN Yamaha Integration

## Overview
Configure <PERSON><PERSON><PERSON> on your Synology NAS (*************) to properly discover and control Yamaha MusicCast speakers on VLAN 3 (***********/24).

## Network Architecture
```
VLAN 1 (***********/24) - Jellyfin Server (*************)
    ↕ (EdgeRouter IGMP Proxy + mDNS)
VLAN 3 (***********/24) - Yamaha Speakers
    ↕ (EdgeRouter Firewall Rules)
VLAN 10 (************/24) - Client Devices (Jelly<PERSON> Apps)
```

## Part 1: Jellyfin Server Configuration

### 1.1 Access Jellyfin Admin Dashboard
1. Navigate to: `http://*************:8096`
2. <PERSON><PERSON> as administrator
3. Go to **Dashboard** → **Settings**

### 1.2 Configure DLNA Settings
1. Go to **Dashboard** → **DLNA**
2. **Enable DLNA server**: ✅ Checked
3. **Enable DLNA PlayTo**: ✅ Checked
4. **DLNA server port number**: `8920` (default)
5. **Blast alive messages**: ✅ Checked (important for cross-VLAN)
6. **Blast alive message interval**: `30` seconds
7. **Default user account**: Select appropriate user
8. Click **Save**

### 1.3 Configure Network Settings
1. Go to **Dashboard** → **Networking**
2. **Local network subnets**: Add these networks:
   ```
   ***********/24
   ***********/24
   ************/24
   ***********/24
   ```
3. **Bind to local network addresses only**: ❌ Unchecked
4. **Enable automatic port mapping**: ✅ Checked
5. **Public HTTP port**: `8096`
6. **Public HTTPS port**: `8920`
7. **Enable UPnP**: ✅ Checked
8. Click **Save**

### 1.4 Configure Playback Settings
1. Go to **Dashboard** → **Playback**
2. **Allow media conversion**: ✅ Checked
3. **Allow audio playback that requires transcoding**: ✅ Checked
4. **Internet streaming bitrate limit**: Set appropriately
5. **Path substitution**: Leave empty unless needed
6. Click **Save**

## Part 2: Synology NAS Network Configuration

### 2.1 Docker Container Network Settings
If Jellyfin is running in Docker:

```yaml
# docker-compose.yml excerpt
services:
  jellyfin:
    image: jellyfin/jellyfin
    container_name: jellyfin
    network_mode: host  # Important for DLNA discovery
    environment:
      - JELLYFIN_PublishedServerUrl=http://*************:8096
    ports:
      - "8096:8096"   # HTTP
      - "8920:8920"   # DLNA
      - "1900:1900/udp"  # UPnP discovery
      - "7359:7359/udp"  # Jellyfin discovery
    volumes:
      - /volume1/docker/jellyfin/config:/config
      - /volume1/docker/jellyfin/cache:/cache
      - /volume1/media:/media
```

### 2.2 Synology Firewall Configuration
1. Go to **Control Panel** → **Security** → **Firewall**
2. Edit rules for **LAN** interface
3. Add these rules:

| Port | Protocol | Source | Action | Description |
|------|----------|---------|---------|-------------|
| 8096 | TCP | All | Allow | Jellyfin HTTP |
| 8920 | TCP | All | Allow | Jellyfin DLNA |
| 1900 | UDP | All | Allow | UPnP Discovery |
| 7359 | UDP | All | Allow | Jellyfin Discovery |

## Part 3: Yamaha Speaker Configuration

### 3.1 MusicCast App Settings
1. Open **MusicCast Controller** app
2. Go to **Settings** → **Network Standby**
3. Enable **Network Standby**: ✅ On
4. Go to **Settings** → **Network Services**
5. Enable **DLNA Server**: ✅ On
6. Enable **Network Services**: ✅ On

### 3.2 Speaker Network Verification
Verify each speaker is on VLAN 3:
- Living Room: ************
- Kitchen: ************
- Bureau: ************
- Bedroom: ************

### 3.3 Speaker DLNA Settings
On each speaker's web interface (http://192.168.3.x):
1. Go to **Network** → **Network Services**
2. Enable **DLNA DMR**: ✅ On
3. Enable **Network Standby**: ✅ On
4. Set **Standby Mode**: **Normal**
5. Apply settings and restart speaker

## Part 4: Testing and Verification

### 4.1 Network Connectivity Tests
From EdgeRouter, test basic connectivity:
```bash
# Test Jellyfin server
ping *************

# Test each Yamaha speaker
ping ************
ping ************
ping ************
ping ************
```

### 4.2 DLNA Discovery Tests
From a client device on VLAN 10:

```bash
# Test UPnP discovery
echo -e "M-SEARCH * HTTP/1.1\r\nHOST: ***************:1900\r\nMAN: \"ssdp:discover\"\r\nST: upnp:rootdevice\r\nMX: 3\r\n\r\n" | nc -u 239.255.************

# Test Jellyfin accessibility
curl -I http://*************:8096

# Test speaker accessibility
curl -I http://************
```

### 4.3 Jellyfin DLNA Device Detection
1. Go to **Jellyfin Dashboard** → **DLNA**
2. Check **Active Devices** section
3. Should show discovered Yamaha speakers
4. Test playback to each device

### 4.4 Client App Testing
1. Open Jellyfin app on mobile/desktop (VLAN 10)
2. Play any audio content
3. Look for **Cast** or **Play To** button
4. Should show Yamaha speakers as available targets
5. Test audio casting to speakers

## Part 5: Advanced Configuration

### 5.1 Custom DLNA Profiles
If speakers don't appear or have issues, create custom profiles:

1. Go to **Dashboard** → **DLNA** → **Profiles**
2. Click **Add Profile**
3. Configure for Yamaha MusicCast:
   ```
   Name: Yamaha MusicCast
   Identification:
     - User Agent: MusicCast
     - Manufacturer: Yamaha
   
   Audio Codecs: MP3, AAC, FLAC, WAV
   Container Formats: MP3, MP4, FLAC
   ```

### 5.2 Network Optimization
For better performance across VLANs:

1. **Increase DLNA timeouts** in Jellyfin
2. **Enable transcoding** for compatibility
3. **Set appropriate bitrates** for network capacity
4. **Monitor network usage** during playback

### 5.3 Logging and Debugging
Enable detailed logging:

1. Go to **Dashboard** → **Logs**
2. Set **Log Level**: **Debug**
3. Monitor logs during DLNA operations
4. Look for discovery and connection errors

## Part 6: Troubleshooting

### 6.1 Common Issues

#### Speakers Not Discovered
- ✅ Check EdgeRouter IGMP proxy configuration
- ✅ Verify mDNS repeater is running
- ✅ Ensure firewall rules allow multicast traffic
- ✅ Restart Jellyfin service
- ✅ Power cycle Yamaha speakers

#### Discovery Works But Casting Fails
- ✅ Check TCP port connectivity (5005, 8080, 49154)
- ✅ Verify speaker DLNA renderer is enabled
- ✅ Test direct HTTP access to speakers
- ✅ Check Jellyfin transcoding settings

#### Intermittent Connectivity
- ✅ Monitor network performance
- ✅ Check for multicast flooding
- ✅ Verify IGMP snooping settings on switches
- ✅ Increase DLNA alive message intervals

### 6.2 Diagnostic Commands

#### On EdgeRouter:
```bash
# Check multicast routing
show protocols igmp-proxy interface
show ip igmp groups

# Monitor DLNA traffic
sudo tcpdump -i any port 1900 or port 8096 or port 8920

# Check firewall logs
show log tail 50 | grep -i drop
```

#### On Synology NAS:
```bash
# Check Jellyfin container logs
docker logs jellyfin

# Test network connectivity
ping ************
telnet ************ 80
```

### 6.3 Performance Optimization

#### Network Settings:
- **IGMP query interval**: 30 seconds
- **Multicast timeout**: 300 seconds
- **DLNA alive interval**: 30 seconds

#### Jellyfin Settings:
- **Transcoding threads**: Match CPU cores
- **Hardware acceleration**: Enable if available
- **Network buffer**: Increase for cross-VLAN

## Expected Results

After proper configuration:

1. **Jellyfin Dashboard** shows Yamaha speakers in DLNA devices
2. **Mobile/Desktop apps** show speakers as cast targets
3. **Audio playback** works seamlessly across VLANs
4. **Speaker control** (play/pause/volume) functions properly
5. **Multi-room audio** synchronization works

## Maintenance

### Weekly:
- Check DLNA device list in Jellyfin
- Verify speaker network connectivity
- Review Jellyfin logs for errors

### Monthly:
- Update Jellyfin to latest version
- Check Yamaha speaker firmware
- Review network performance metrics

Your cross-VLAN DLNA setup should now work perfectly with Jellyfin discovering and controlling Yamaha speakers across network segments!
