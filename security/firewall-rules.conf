# Comprehensive EdgeRouter Firewall Rules
# These rules ensure services are only accessible via VPN and local network
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - Guest Network: **********/24 (VLAN 2 - Internet only, isolated)
# - VPN Network: ***********/24

echo "=== EdgeRouter Firewall Configuration for VPN-Only Access with Guest Isolation ==="
echo "Network Configuration:"
echo "- Wired LAN: ***********/24 (Synology NAS: *************)"
echo "- WiFi LAN: ***********/24"
echo "- Guest Network: **********/24 (ISOLATED - Internet only)"
echo "- VPN Network: ***********/24"
echo ""
echo "SECURITY: Guest network will be completely isolated from all internal networks"
echo ""

echo "1. Create firewall groups for easy management:"
echo "configure"
echo ""

echo "# Create network groups"
echo "set firewall group network-group LOCAL_NETWORKS network ***********/24"
echo "set firewall group network-group LOCAL_NETWORKS network ***********/24"
echo "set firewall group network-group LOCAL_NETWORKS network ***********/24"
echo "set firewall group network-group LOCAL_NETWORKS description 'Local and VPN networks (excludes guest)'"
echo ""

echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS description 'All internal networks (excludes guest)'"
echo ""

echo "set firewall group network-group VPN_NETWORK network ***********/24"
echo "set firewall group network-group VPN_NETWORK description 'WireGuard VPN network'"
echo ""

echo "set firewall group network-group WIRED_LAN network ***********/24"
echo "set firewall group network-group WIRED_LAN description 'Wired LAN network'"
echo ""

echo "set firewall group network-group WIFI_LAN network ***********/24"
echo "set firewall group network-group WIFI_LAN description 'WiFi LAN network'"
echo ""

echo "set firewall group network-group GUEST_NETWORK network **********/24"
echo "set firewall group network-group GUEST_NETWORK description 'Guest network (isolated)'"
echo ""

echo "# Create port groups for services"
echo "set firewall group port-group WEB_PORTS port 80"
echo "set firewall group port-group WEB_PORTS port 443"
echo "set firewall group port-group WEB_PORTS description 'Standard HTTP and HTTPS ports'"
echo ""

echo "set firewall group port-group TRAEFIK_PORTS port 8080"
echo "set firewall group port-group TRAEFIK_PORTS port 8443"
echo "set firewall group port-group TRAEFIK_PORTS port 9080"
echo "set firewall group port-group TRAEFIK_PORTS description 'Traefik alternative ports'"
echo ""

echo "set firewall group port-group MANAGEMENT_PORTS port 22"
echo "set firewall group port-group MANAGEMENT_PORTS port 3000"
echo "set firewall group port-group MANAGEMENT_PORTS port 5000"
echo "set firewall group port-group MANAGEMENT_PORTS port 5001"
echo "set firewall group port-group MANAGEMENT_PORTS description 'Management interface ports'"
echo ""

echo "set firewall group port-group SYNOLOGY_PORTS port 80"
echo "set firewall group port-group SYNOLOGY_PORTS port 443"
echo "set firewall group port-group SYNOLOGY_PORTS port 5000"
echo "set firewall group port-group SYNOLOGY_PORTS port 5001"
echo "set firewall group port-group SYNOLOGY_PORTS description 'Synology NAS web interface ports'"
echo ""

echo "set firewall group port-group ALL_SERVICE_PORTS port 80"
echo "set firewall group port-group ALL_SERVICE_PORTS port 443"
echo "set firewall group port-group ALL_SERVICE_PORTS port 3000"    # AdGuard Home
echo "set firewall group port-group ALL_SERVICE_PORTS port 5000"    # Synology DSM HTTP
echo "set firewall group port-group ALL_SERVICE_PORTS port 5001"    # Synology DSM HTTPS
echo "set firewall group port-group ALL_SERVICE_PORTS port 8080"    # Traefik HTTP
echo "set firewall group port-group ALL_SERVICE_PORTS port 8096"    # Jellyfin
echo "set firewall group port-group ALL_SERVICE_PORTS port 8123"    # Home Assistant
echo "set firewall group port-group ALL_SERVICE_PORTS port 8443"    # Traefik HTTPS / UniFi
echo "set firewall group port-group ALL_SERVICE_PORTS port 9000"    # Portainer
echo "set firewall group port-group ALL_SERVICE_PORTS port 9080"    # Traefik Dashboard
echo "set firewall group port-group ALL_SERVICE_PORTS description 'All internal service ports'"
echo ""

echo "set firewall group port-group CONTAINER_PORTS port 3000"     # AdGuard Home
echo "set firewall group port-group CONTAINER_PORTS port 8080"     # Custom services
echo "set firewall group port-group CONTAINER_PORTS port 8081"     # Media app
echo "set firewall group port-group CONTAINER_PORTS port 8082"     # Media API
echo "set firewall group port-group CONTAINER_PORTS port 8096"     # Jellyfin
echo "set firewall group port-group CONTAINER_PORTS port 8123"     # Home Assistant
echo "set firewall group port-group CONTAINER_PORTS port 8443"     # UniFi Controller
echo "set firewall group port-group CONTAINER_PORTS port 8880"     # Hotspot Manager
echo "set firewall group port-group CONTAINER_PORTS port 9000"     # Portainer
echo "set firewall group port-group CONTAINER_PORTS port 9080"     # Traefik Dashboard
echo "set firewall group port-group CONTAINER_PORTS description 'Containerized service ports'"
echo ""

echo "2. Configure WAN_IN rules (Internet to Internal Network):"
echo "# Clear existing rules first"
echo "delete firewall name WAN_IN"
echo ""

echo "set firewall name WAN_IN default-action drop"
echo "set firewall name WAN_IN description 'WAN to internal - VPN only'"
echo ""

echo "# Allow WireGuard VPN"
echo "set firewall name WAN_IN rule 10 action accept"
echo "set firewall name WAN_IN rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_IN rule 10 destination port 51820"
echo "set firewall name WAN_IN rule 10 protocol udp"
echo ""

echo "# BLOCK all HTTP/HTTPS from WAN (this is the key security rule)"
echo "set firewall name WAN_IN rule 20 action drop"
echo "set firewall name WAN_IN rule 20 description 'Block HTTP/HTTPS from WAN'"
echo "set firewall name WAN_IN rule 20 destination port-group WEB_PORTS"
echo "set firewall name WAN_IN rule 20 protocol tcp"
echo "set firewall name WAN_IN rule 20 log enable"
echo ""

echo "# BLOCK Traefik alternative ports from WAN"
echo "set firewall name WAN_IN rule 25 action drop"
echo "set firewall name WAN_IN rule 25 description 'Block Traefik ports from WAN'"
echo "set firewall name WAN_IN rule 25 destination port-group TRAEFIK_PORTS"
echo "set firewall name WAN_IN rule 25 protocol tcp"
echo "set firewall name WAN_IN rule 25 log enable"
echo ""

echo "# BLOCK management ports from WAN"
echo "set firewall name WAN_IN rule 26 action drop"
echo "set firewall name WAN_IN rule 26 description 'Block management ports from WAN'"
echo "set firewall name WAN_IN rule 26 destination port-group MANAGEMENT_PORTS"
echo "set firewall name WAN_IN rule 26 protocol tcp"
echo "set firewall name WAN_IN rule 26 log enable"
echo ""

echo "# BLOCK Synology ports from WAN"
echo "set firewall name WAN_IN rule 27 action drop"
echo "set firewall name WAN_IN rule 27 description 'Block Synology NAS ports from WAN'"
echo "set firewall name WAN_IN rule 27 destination port-group SYNOLOGY_PORTS"
echo "set firewall name WAN_IN rule 27 protocol tcp"
echo "set firewall name WAN_IN rule 27 log enable"
echo ""

echo "# BLOCK ALL containerized service ports from WAN"
echo "set firewall name WAN_IN rule 28 action drop"
echo "set firewall name WAN_IN rule 28 description 'Block all container service ports from WAN'"
echo "set firewall name WAN_IN rule 28 destination port-group CONTAINER_PORTS"
echo "set firewall name WAN_IN rule 28 protocol tcp"
echo "set firewall name WAN_IN rule 28 log enable"
echo ""

echo "# BLOCK direct access to Synology NAS from WAN"
echo "set firewall name WAN_IN rule 29 action drop"
echo "set firewall name WAN_IN rule 29 description 'Block direct Synology NAS access from WAN'"
echo "set firewall name WAN_IN rule 29 destination address *************"
echo "set firewall name WAN_IN rule 29 protocol tcp"
echo "set firewall name WAN_IN rule 29 log enable"
echo ""

echo "# Allow established and related connections"
echo "set firewall name WAN_IN rule 30 action accept"
echo "set firewall name WAN_IN rule 30 description 'Allow established/related'"
echo "set firewall name WAN_IN rule 30 state established enable"
echo "set firewall name WAN_IN rule 30 state related enable"
echo ""

echo "3. Configure WAN_LOCAL rules (Internet to Router):"
echo "# Clear existing rules first"
echo "delete firewall name WAN_LOCAL"
echo ""

echo "set firewall name WAN_LOCAL default-action drop"
echo "set firewall name WAN_LOCAL description 'WAN to router - VPN and SSH only'"
echo ""

echo "# Allow WireGuard VPN"
echo "set firewall name WAN_LOCAL rule 10 action accept"
echo "set firewall name WAN_LOCAL rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_LOCAL rule 10 destination port 51820"
echo "set firewall name WAN_LOCAL rule 10 protocol udp"
echo ""

echo "# Allow SSH (consider restricting this further or disabling)"
echo "set firewall name WAN_LOCAL rule 20 action accept"
echo "set firewall name WAN_LOCAL rule 20 description 'Allow SSH'"
echo "set firewall name WAN_LOCAL rule 20 destination port 22"
echo "set firewall name WAN_LOCAL rule 20 protocol tcp"
echo ""

echo "# BLOCK web interface access from WAN"
echo "set firewall name WAN_LOCAL rule 25 action drop"
echo "set firewall name WAN_LOCAL rule 25 description 'Block web interface from WAN'"
echo "set firewall name WAN_LOCAL rule 25 destination port 80,443"
echo "set firewall name WAN_LOCAL rule 25 protocol tcp"
echo "set firewall name WAN_LOCAL rule 25 log enable"
echo ""

echo "# Allow established and related"
echo "set firewall name WAN_LOCAL rule 30 action accept"
echo "set firewall name WAN_LOCAL rule 30 description 'Allow established/related'"
echo "set firewall name WAN_LOCAL rule 30 state established enable"
echo "set firewall name WAN_LOCAL rule 30 state related enable"
echo ""

echo "4. Configure LAN_IN rules (Internal network access control):"
echo "set firewall name WIRED_LAN_IN default-action accept"
echo "set firewall name WIRED_LAN_IN description 'Wired LAN to internal'"
echo ""

echo "# Allow wired LAN access to services"
echo "set firewall name WIRED_LAN_IN rule 10 action accept"
echo "set firewall name WIRED_LAN_IN rule 10 description 'Allow wired LAN to services'"
echo "set firewall name WIRED_LAN_IN rule 10 source group network-group WIRED_LAN"
echo ""

echo "set firewall name WIFI_LAN_IN default-action accept"
echo "set firewall name WIFI_LAN_IN description 'WiFi LAN to internal'"
echo ""

echo "# Allow WiFi LAN access to services"
echo "set firewall name WIFI_LAN_IN rule 10 action accept"
echo "set firewall name WIFI_LAN_IN rule 10 description 'Allow WiFi LAN to services'"
echo "set firewall name WIFI_LAN_IN rule 10 source group network-group WIFI_LAN"
echo ""

echo "5. Configure VPN_IN rules (VPN network access control):"
echo "set firewall name VPN_IN default-action accept"
echo "set firewall name VPN_IN description 'VPN to internal'"
echo ""

echo "# Allow VPN clients to access wired LAN services"
echo "set firewall name VPN_IN rule 10 action accept"
echo "set firewall name VPN_IN rule 10 description 'Allow VPN to wired LAN services'"
echo "set firewall name VPN_IN rule 10 source group network-group VPN_NETWORK"
echo "set firewall name VPN_IN rule 10 destination group network-group WIRED_LAN"
echo ""

echo "# Allow VPN clients to access WiFi LAN services"
echo "set firewall name VPN_IN rule 20 action accept"
echo "set firewall name VPN_IN rule 20 description 'Allow VPN to WiFi LAN services'"
echo "set firewall name VPN_IN rule 20 source group network-group VPN_NETWORK"
echo "set firewall name VPN_IN rule 20 destination group network-group WIFI_LAN"
echo ""

echo "7. Configure GUEST_IN rules (Guest network isolation):"
echo "set firewall name GUEST_IN default-action drop"
echo "set firewall name GUEST_IN description 'Guest network isolation - Internet only'"
echo ""

echo "# BLOCK guest access to wired LAN"
echo "set firewall name GUEST_IN rule 10 action drop"
echo "set firewall name GUEST_IN rule 10 description 'Block guest access to wired LAN'"
echo "set firewall name GUEST_IN rule 10 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 10 destination group network-group WIRED_LAN"
echo "set firewall name GUEST_IN rule 10 log enable"
echo ""

echo "# BLOCK guest access to WiFi LAN"
echo "set firewall name GUEST_IN rule 20 action drop"
echo "set firewall name GUEST_IN rule 20 description 'Block guest access to WiFi LAN'"
echo "set firewall name GUEST_IN rule 20 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 20 destination group network-group WIFI_LAN"
echo "set firewall name GUEST_IN rule 20 log enable"
echo ""

echo "# BLOCK guest access to VPN network"
echo "set firewall name GUEST_IN rule 30 action drop"
echo "set firewall name GUEST_IN rule 30 description 'Block guest access to VPN network'"
echo "set firewall name GUEST_IN rule 30 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 30 destination group network-group VPN_NETWORK"
echo "set firewall name GUEST_IN rule 30 log enable"
echo ""

echo "# BLOCK guest access to EdgeRouter management"
echo "set firewall name GUEST_IN rule 40 action drop"
echo "set firewall name GUEST_IN rule 40 description 'Block guest access to EdgeRouter'"
echo "set firewall name GUEST_IN rule 40 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 40 destination address ***********"
echo "set firewall name GUEST_IN rule 40 log enable"
echo ""

echo "# BLOCK guest access to internal DNS servers"
echo "set firewall name GUEST_IN rule 50 action drop"
echo "set firewall name GUEST_IN rule 50 description 'Block guest access to internal DNS'"
echo "set firewall name GUEST_IN rule 50 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 50 destination address *************"
echo "set firewall name GUEST_IN rule 50 destination port 53"
echo "set firewall name GUEST_IN rule 50 protocol udp"
echo "set firewall name GUEST_IN rule 50 log enable"
echo ""

echo "# BLOCK guest access to WireGuard VPN server"
echo "set firewall name GUEST_IN rule 60 action drop"
echo "set firewall name GUEST_IN rule 60 description 'Block guest access to VPN server'"
echo "set firewall name GUEST_IN rule 60 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 60 destination address ***********"
echo "set firewall name GUEST_IN rule 60 destination port 51820"
echo "set firewall name GUEST_IN rule 60 protocol udp"
echo "set firewall name GUEST_IN rule 60 log enable"
echo ""

echo "# BLOCK guest access to ALL containerized services"
echo "set firewall name GUEST_IN rule 65 action drop"
echo "set firewall name GUEST_IN rule 65 description 'Block guest to all container services'"
echo "set firewall name GUEST_IN rule 65 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 65 destination address *************"
echo "set firewall name GUEST_IN rule 65 destination port-group CONTAINER_PORTS"
echo "set firewall name GUEST_IN rule 65 log enable"
echo "set firewall name GUEST_IN rule 65 protocol tcp"
echo ""

echo "# BLOCK guest access to static devices (Yamaha speakers, etc.)"
echo "set firewall name GUEST_IN rule 66 action drop"
echo "set firewall name GUEST_IN rule 66 description 'Block guest to static devices'"
echo "set firewall name GUEST_IN rule 66 source group network-group GUEST_NETWORK"
echo "set firewall name GUEST_IN rule 66 destination group network-group WIRED_LAN"
echo "set firewall name GUEST_IN rule 66 destination port 80,443,8080,8123"
echo "set firewall name GUEST_IN rule 66 log enable"
echo "set firewall name GUEST_IN rule 66 protocol tcp"
echo ""

echo "# Allow established and related connections (for internet access)"
echo "set firewall name GUEST_IN rule 70 action accept"
echo "set firewall name GUEST_IN rule 70 description 'Allow established/related for internet'"
echo "set firewall name GUEST_IN rule 70 state established enable"
echo "set firewall name GUEST_IN rule 70 state related enable"
echo ""

echo "8. Apply firewall rules to interfaces:"
echo "set interfaces ethernet eth0 firewall in name WAN_IN"
echo "set interfaces ethernet eth0 firewall local name WAN_LOCAL"
echo "set interfaces ethernet eth1 firewall in name WIRED_LAN_IN"
echo "# If WiFi is on separate interface (adjust interface name as needed):"
echo "# set interfaces ethernet eth2 firewall in name WIFI_LAN_IN"
echo "set interfaces wireguard wg0 firewall in name VPN_IN"
echo ""
echo "# Apply guest network isolation (adjust VLAN interface as needed)"
echo "# For VLAN 2 guest network:"
echo "set interfaces ethernet eth1 vif 2 firewall in name GUEST_IN"
echo "# OR if guest network is on separate interface:"
echo "# set interfaces ethernet eth3 firewall in name GUEST_IN"
echo ""

echo "9. Configure NAT for guest network (internet access only):"
echo "set service nat rule 200 description 'Guest network to internet'"
echo "set service nat rule 200 outbound-interface eth0"
echo "set service nat rule 200 source address **********/24"
echo "set service nat rule 200 type masquerade"
echo ""

echo "10. CRITICAL: Remove all port forwarding rules:"
echo "# List current NAT rules to see what needs to be removed"
echo "show service nat rule"
echo ""
echo "# Remove port forwarding rules (replace X with actual rule numbers)"
echo "# delete service nat rule X"
echo "# Example:"
echo "# delete service nat rule 1  # HTTP forwarding"
echo "# delete service nat rule 2  # HTTPS forwarding"
echo ""

echo "11. Verify guest network isolation:"
echo "# Check that guest network cannot reach internal networks"
echo "show firewall name GUEST_IN"
echo "show log | grep GUEST_IN"
echo ""

echo "12. Commit and save:"
echo "commit"
echo "save"
echo "exit"
