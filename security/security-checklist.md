# Security Checklist for VPN-Only Access

## Pre-Implementation Security Review

### 1. Current Exposure Assessment
- [ ] Document all currently exposed services and ports
- [ ] Identify all port forwarding rules on EdgeRouter
- [ ] List all public DNS entries pointing to your IP
- [ ] Check for any UPnP enabled devices

### 2. Backup Current Configuration
- [ ] Backup EdgeRouter configuration: `show configuration commands`
- [ ] Backup Traefik configuration files
- [ ] Backup AdGuard Home settings
- [ ] Document current network topology

## Implementation Security Measures

### 3. EdgeRouter Security
- [ ] Remove ALL port forwarding rules for HTTP/HTTPS (ports 80, 443)
- [ ] Implement firewall rules to block WAN access to internal services
- [ ] Enable firewall logging for dropped connections
- [ ] Disable UPnP if enabled
- [ ] Change default SSH port (optional but recommended)
- [ ] Implement fail2ban for SSH protection (if available)

### 4. WireGuard Security
- [ ] Use strong, unique private keys for server and each client
- [ ] Limit client allowed-ips to only necessary networks
- [ ] Use different IP addresses for each client
- [ ] Enable PersistentKeepalive for mobile clients
- [ ] Regularly rotate WireGuard keys (quarterly recommended)

### 5. Traefik Security
- [ ] Implement IP whitelisting middleware for internal networks only
- [ ] Enable security headers middleware
- [ ] Use strong TLS configuration (TLS 1.2+ only)
- [ ] Implement rate limiting
- [ ] Enable access logging
- [ ] Disable Traefik API/dashboard from public access

### 6. DNS Security
- [ ] Configure AdGuard Home to only accept queries from local/VPN networks
- [ ] Implement DNS rewrites for internal resolution
- [ ] Disable public DNS resolution for your subdomains (if possible)
- [ ] Use secure DNS upstream servers (*******, *******)

## Post-Implementation Verification

### 7. External Security Testing
- [ ] Test that services are NOT accessible from external networks
- [ ] Verify WireGuard VPN connection works
- [ ] Test DNS resolution works when connected via VPN
- [ ] Confirm SSL certificates still work for internal access
- [ ] Test from different external networks (mobile data, different ISP)

### 8. Internal Access Testing
- [ ] Verify local network access still works
- [ ] Test VPN client access from different devices
- [ ] Confirm all services accessible via VPN
- [ ] Test DNS resolution for all subdomains
- [ ] Verify SSL/TLS certificates work properly

### 9. Monitoring and Alerting
- [ ] Set up monitoring for VPN connection attempts
- [ ] Monitor firewall logs for blocked connection attempts
- [ ] Set up alerts for failed VPN authentication
- [ ] Monitor certificate expiration dates
- [ ] Set up uptime monitoring for critical services

## Ongoing Security Maintenance

### 10. Regular Security Tasks
- [ ] Monthly: Review firewall logs for suspicious activity
- [ ] Monthly: Update WireGuard client configurations if needed
- [ ] Quarterly: Rotate WireGuard keys
- [ ] Quarterly: Review and update firewall rules
- [ ] Annually: Security audit of entire setup

### 11. Emergency Procedures
- [ ] Document procedure to quickly re-enable public access if needed
- [ ] Create emergency VPN client configuration for critical access
- [ ] Document rollback procedures for all configuration changes
- [ ] Maintain offline backup of all configuration files

## Security Best Practices

### 12. Additional Recommendations
- [ ] Use strong, unique passwords for all services
- [ ] Enable two-factor authentication where available
- [ ] Regularly update all software and firmware
- [ ] Use network segmentation for IoT devices
- [ ] Implement intrusion detection system (IDS) if possible
- [ ] Regular security scanning of internal network

### 13. Documentation
- [ ] Document all configuration changes
- [ ] Maintain network diagram with security zones
- [ ] Keep inventory of all devices and services
- [ ] Document incident response procedures

## Risk Assessment

### High Risk Items (Address Immediately)
- Public access to administrative interfaces
- Weak or default passwords
- Unencrypted management protocols
- Missing security updates

### Medium Risk Items (Address Soon)
- Overly permissive firewall rules
- Lack of monitoring/alerting
- Missing backup procedures
- Weak TLS configuration

### Low Risk Items (Address When Convenient)
- Missing security headers
- Lack of rate limiting
- Verbose error messages
- Missing security documentation
