# Guest Network Isolation Configuration
# Complete security configuration for isolating guest network from internal resources

echo "=== Guest Network Isolation Configuration ==="
echo "Network: **********/24 (VLAN 2)"
echo "Security Policy: Internet access only, complete isolation from internal networks"
echo ""

# Guest Network Security Requirements:
# 1. Block access to all internal networks (***********/24, ***********/24, ***********/24)
# 2. Block access to EdgeRouter management (***********)
# 3. Block access to internal DNS servers (***********13)
# 4. Block access to WireGuard VPN server (port 51820)
# 5. Block access to all internal services (Traefik, AdGuard, Synology, etc.)
# 6. Allow internet access only

echo "1. Configure VLAN interface for guest network:"
echo "configure"
echo ""

echo "# Create VLAN 2 interface for guest network"
echo "set interfaces ethernet eth1 vif 2 address **********/24"
echo "set interfaces ethernet eth1 vif 2 description 'Guest Network - Isolated'"
echo ""

echo "# Configure DHCP for guest network"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 default-router **********"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 dns-server *******"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 lease 86400"
echo "set service dhcp-server shared-network-name GUEST subnet **********/24 start **********0 stop **********00"
echo ""

echo "2. Create comprehensive guest isolation firewall rules:"
echo ""

echo "# Create guest network firewall ruleset"
echo "set firewall name GUEST_ISOLATION default-action drop"
echo "set firewall name GUEST_ISOLATION description 'Guest network complete isolation'"
echo ""

echo "# Rule 10: BLOCK access to wired LAN (including Synology NAS)"
echo "set firewall name GUEST_ISOLATION rule 10 action drop"
echo "set firewall name GUEST_ISOLATION rule 10 description 'Block guest to wired LAN'"
echo "set firewall name GUEST_ISOLATION rule 10 destination address ***********/24"
echo "set firewall name GUEST_ISOLATION rule 10 log enable"
echo "set firewall name GUEST_ISOLATION rule 10 protocol all"
echo ""

echo "# Rule 20: BLOCK access to WiFi LAN"
echo "set firewall name GUEST_ISOLATION rule 20 action drop"
echo "set firewall name GUEST_ISOLATION rule 20 description 'Block guest to WiFi LAN'"
echo "set firewall name GUEST_ISOLATION rule 20 destination address ***********/24"
echo "set firewall name GUEST_ISOLATION rule 20 log enable"
echo "set firewall name GUEST_ISOLATION rule 20 protocol all"
echo ""

echo "# Rule 30: BLOCK access to VPN network"
echo "set firewall name GUEST_ISOLATION rule 30 action drop"
echo "set firewall name GUEST_ISOLATION rule 30 description 'Block guest to VPN network'"
echo "set firewall name GUEST_ISOLATION rule 30 destination address ***********/24"
echo "set firewall name GUEST_ISOLATION rule 30 log enable"
echo "set firewall name GUEST_ISOLATION rule 30 protocol all"
echo ""

echo "# Rule 40: BLOCK access to EdgeRouter management interface"
echo "set firewall name GUEST_ISOLATION rule 40 action drop"
echo "set firewall name GUEST_ISOLATION rule 40 description 'Block guest to EdgeRouter management'"
echo "set firewall name GUEST_ISOLATION rule 40 destination address ***********"
echo "set firewall name GUEST_ISOLATION rule 40 log enable"
echo "set firewall name GUEST_ISOLATION rule 40 protocol all"
echo ""

echo "# Rule 50: BLOCK access to guest gateway management"
echo "set firewall name GUEST_ISOLATION rule 50 action drop"
echo "set firewall name GUEST_ISOLATION rule 50 description 'Block guest to gateway management'"
echo "set firewall name GUEST_ISOLATION rule 50 destination address **********"
echo "set firewall name GUEST_ISOLATION rule 50 destination port 22,80,443"
echo "set firewall name GUEST_ISOLATION rule 50 log enable"
echo "set firewall name GUEST_ISOLATION rule 50 protocol tcp"
echo ""

echo "# Rule 60: BLOCK access to WireGuard VPN server"
echo "set firewall name GUEST_ISOLATION rule 60 action drop"
echo "set firewall name GUEST_ISOLATION rule 60 description 'Block guest to VPN server'"
echo "set firewall name GUEST_ISOLATION rule 60 destination address ***********"
echo "set firewall name GUEST_ISOLATION rule 60 destination port 51820"
echo "set firewall name GUEST_ISOLATION rule 60 log enable"
echo "set firewall name GUEST_ISOLATION rule 60 protocol udp"
echo ""

echo "# Rule 70: BLOCK access to internal DNS servers"
echo "set firewall name GUEST_ISOLATION rule 70 action drop"
echo "set firewall name GUEST_ISOLATION rule 70 description 'Block guest to internal DNS'"
echo "set firewall name GUEST_ISOLATION rule 70 destination address ***********13"
echo "set firewall name GUEST_ISOLATION rule 70 destination port 53"
echo "set firewall name GUEST_ISOLATION rule 70 log enable"
echo "set firewall name GUEST_ISOLATION rule 70 protocol udp"
echo ""

echo "# Rule 80: BLOCK access to Synology NAS services"
echo "set firewall name GUEST_ISOLATION rule 80 action drop"
echo "set firewall name GUEST_ISOLATION rule 80 description 'Block guest to Synology services'"
echo "set firewall name GUEST_ISOLATION rule 80 destination address ***********13"
echo "set firewall name GUEST_ISOLATION rule 80 destination port 80,443,5000,5001,8080,8443,9080,3000"
echo "set firewall name GUEST_ISOLATION rule 80 log enable"
echo "set firewall name GUEST_ISOLATION rule 80 protocol tcp"
echo ""

echo "# Rule 90: BLOCK inter-guest communication (optional security enhancement)"
echo "set firewall name GUEST_ISOLATION rule 90 action drop"
echo "set firewall name GUEST_ISOLATION rule 90 description 'Block guest-to-guest communication'"
echo "set firewall name GUEST_ISOLATION rule 90 destination address **********/24"
echo "set firewall name GUEST_ISOLATION rule 90 log enable"
echo "set firewall name GUEST_ISOLATION rule 90 protocol all"
echo ""

echo "# Rule 100: Allow established and related connections (for internet access)"
echo "set firewall name GUEST_ISOLATION rule 100 action accept"
echo "set firewall name GUEST_ISOLATION rule 100 description 'Allow established/related'"
echo "set firewall name GUEST_ISOLATION rule 100 state established enable"
echo "set firewall name GUEST_ISOLATION rule 100 state related enable"
echo ""

echo "# Rule 110: Allow DNS to public servers only"
echo "set firewall name GUEST_ISOLATION rule 110 action accept"
echo "set firewall name GUEST_ISOLATION rule 110 description 'Allow public DNS'"
echo "set firewall name GUEST_ISOLATION rule 110 destination port 53"
echo "set firewall name GUEST_ISOLATION rule 110 protocol udp"
echo "# This allows DNS but blocks internal DNS via rule 70"
echo ""

echo "# Rule 120: Allow HTTP/HTTPS to internet"
echo "set firewall name GUEST_ISOLATION rule 120 action accept"
echo "set firewall name GUEST_ISOLATION rule 120 description 'Allow HTTP/HTTPS to internet'"
echo "set firewall name GUEST_ISOLATION rule 120 destination port 80,443"
echo "set firewall name GUEST_ISOLATION rule 120 protocol tcp"
echo ""

echo "3. Apply firewall rules to guest VLAN interface:"
echo "set interfaces ethernet eth1 vif 2 firewall in name GUEST_ISOLATION"
echo ""

echo "4. Configure NAT for guest network internet access:"
echo "set service nat rule 300 description 'Guest network to internet'"
echo "set service nat rule 300 outbound-interface eth0"
echo "set service nat rule 300 source address **********/24"
echo "set service nat rule 300 type masquerade"
echo ""

echo "5. Configure guest network local firewall rules (router protection):"
echo "set firewall name GUEST_LOCAL default-action drop"
echo "set firewall name GUEST_LOCAL description 'Protect router from guest network'"
echo ""

echo "# Block guest access to router services"
echo "set firewall name GUEST_LOCAL rule 10 action drop"
echo "set firewall name GUEST_LOCAL rule 10 description 'Block guest to router services'"
echo "set firewall name GUEST_LOCAL rule 10 source address **********/24"
echo "set firewall name GUEST_LOCAL rule 10 log enable"
echo ""

echo "# Allow established connections"
echo "set firewall name GUEST_LOCAL rule 20 action accept"
echo "set firewall name GUEST_LOCAL rule 20 description 'Allow established/related'"
echo "set firewall name GUEST_LOCAL rule 20 state established enable"
echo "set firewall name GUEST_LOCAL rule 20 state related enable"
echo ""

echo "# Apply local firewall to guest VLAN"
echo "set interfaces ethernet eth1 vif 2 firewall local name GUEST_LOCAL"
echo ""

echo "6. Commit and save configuration:"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "7. Verification commands:"
echo "# Test guest network isolation"
echo "show firewall name GUEST_ISOLATION"
echo "show log | grep GUEST_ISOLATION"
echo ""
echo "# Test from guest device (should fail):"
echo "# ping ***********        # EdgeRouter"
echo "# ping ***********13      # Synology NAS"
echo "# ping ***********        # WiFi gateway"
echo "# ping ***********        # VPN gateway"
echo ""
echo "# Test internet access (should work):"
echo "# ping *******            # Google DNS"
echo "# curl http://www.google.com"
echo ""

echo "=== Guest Network Security Summary ==="
echo "✅ Guest network: **********/24 (VLAN 2)"
echo "✅ Internet access: Allowed via NAT"
echo "❌ Wired LAN access: BLOCKED (***********/24)"
echo "❌ WiFi LAN access: BLOCKED (***********/24)"
echo "❌ VPN network access: BLOCKED (***********/24)"
echo "❌ EdgeRouter management: BLOCKED (***********)"
echo "❌ Internal DNS: BLOCKED (***********13:53)"
echo "❌ WireGuard VPN: BLOCKED (***********:51820)"
echo "❌ Synology services: BLOCKED (all ports)"
echo "❌ Traefik services: BLOCKED (8080, 8443, 9080)"
echo "❌ Guest-to-guest: BLOCKED (optional)"
echo "✅ Public DNS: Allowed (*******, *******)"
echo "✅ HTTP/HTTPS: Allowed to internet only"
