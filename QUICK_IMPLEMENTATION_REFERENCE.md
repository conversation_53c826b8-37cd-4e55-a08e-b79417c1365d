# Quick Implementation Reference - Home Network Setup

## 🚀 **Implementation Phases Overview**

### **Phase 1: Hardware Setup** (2-4 hours)
**Goal**: Physical infrastructure and basic connectivity
- ✅ Equipment placement and power
- ✅ Cable connections and verification
- ✅ Basic device accessibility
- 📋 **Guide**: [HARDWARE_IMPLEMENTATION_CHECKLIST.md](HARDWARE_IMPLEMENTATION_CHECKLIST.md)

### **Phase 2: Core Network** (1-2 hours)
**Goal**: Basic network functionality
- ✅ EdgeRouter basic configuration
- ✅ Synology DS923+ LAG setup
- ✅ USW Flex initial configuration
- 📋 **Config**: `optimization/ds923-dual-nic-topology.conf`

### **Phase 3: Network Segmentation** (2-3 hours)
**Goal**: VLAN implementation and routing
- ✅ Apply complete EdgeRouter configuration
- ✅ Configure UniFi Controller
- ✅ Set up all 7 network segments
- 📋 **Config**: `optimization/unifi-controller-config.json`

### **Phase 4: Security Implementation** (1-2 hours)
**Goal**: VPN-only access and firewall rules
- ✅ Apply enhanced firewall rules
- ✅ Configure WireGuard VPN
- ✅ Block all external access except VPN
- 📋 **Config**: `optimization/ubiquiti-firewall-rules.conf`

### **Phase 5: Services & DNS** (2-3 hours)
**Goal**: Service accessibility and DNS resolution
- ✅ AdGuard Home setup
- ✅ Traefik reverse proxy
- ✅ DNS rewrites and service discovery
- 📋 **Config**: `traefik/docker-compose.yml`

### **Phase 6: Testing & Validation** (1-2 hours)
**Goal**: Comprehensive functionality verification
- ✅ Network connectivity tests
- ✅ Performance validation
- ✅ Security verification
- 📋 **Scripts**: `testing/ds923-lag-comprehensive-test.sh`

## 🔧 **Critical Configuration Commands**

### **EdgeRouter Configuration**
```bash
# Apply complete network configuration
sudo bash optimization/ds923-dual-nic-topology.conf

# Verify configuration
show configuration
show interfaces
show dhcp leases
```

### **Synology LAG Setup**
1. **DSM Web Interface**: Control Panel → Network → Network Interface
2. **Create Bond**: Select both LAN ports
3. **Mode**: Link Aggregation (802.3ad)
4. **IP**: *************/24, Gateway: ***********

### **USW Flex LAG Configuration**
1. **Port Profile**: Create LAG profile for ports 2-3
2. **VLAN**: Set to "All" or trunk mode
3. **Link Aggregation**: Enable on switch side

### **WireGuard VPN Setup**
```bash
# Generate client keys
bash clients/generate-client-keys.sh

# Apply WireGuard configuration
sudo cp edgerouter/wireguard-server.conf /config/auth/
sudo configure
set interfaces wireguard wg0 ...
commit
save
```

## 📊 **Network Architecture Quick Reference**

### **IP Address Allocation**
| Device | IP Address | Purpose |
|--------|------------|---------|
| EdgeRouter 4 | *********** | Gateway & VPN server |
| USW Flex 2.5G | *********** | Core managed switch |
| UniFi AC Pro | *********** | Primary WiFi coverage |
| UniFi U6 Lite | *********** | High-performance WiFi |
| 24-Port Switch | *********** | Access layer switch |
| 4-Port PoE Switch | *********** | UniFi AP power |
| Synology DS923+ | ************* | NAS with LAG |

### **VLAN Configuration**
| VLAN | Network | Purpose | Access Level |
|------|---------|---------|--------------|
| 1 | ***********/24 | Core & wired devices | Full trusted |
| 2 | **********/24 | Guest network | Internet only |
| 3 | ***********/24 | Media network | DLNA + internet |
| 4 | ***********/24 | IoT trusted | Home Assistant |
| 5 | ***********/24 | VPN network | Full admin access |
| 6 | ***********/24 | IoT untrusted | Internet only |
| 10 | ************/24 | WiFi users | Full internal |

### **Port Assignments**
| Switch | Port | Connection | Purpose |
|--------|------|------------|---------|
| USW Flex | 1 | EdgeRouter 4 | Gateway connection |
| USW Flex | 2 | DS923+ NIC 1 | LAG Member 1 |
| USW Flex | 3 | DS923+ NIC 2 | LAG Member 2 |
| USW Flex | 4 | 24-Port Switch | Wired devices |
| USW Flex | 5 | 4-Port PoE | UniFi APs |

## 🛡️ **Security Configuration Quick Reference**

### **Firewall Rules Summary**
- ✅ **WAN**: Only WireGuard (51820/UDP) allowed
- ✅ **LAN**: Full access to internal services
- ✅ **Guest**: Internet only, blocked from internal
- ✅ **IoT**: Restricted based on trust level
- ✅ **VPN**: Full administrative access

### **Service Port Assignments**
| Service | Port | Access Method |
|---------|------|---------------|
| Synology DSM | 80/443 | VPN only |
| Traefik HTTP | 8080 | VPN only |
| Traefik HTTPS | 8443 | VPN only |
| Traefik Dashboard | 9080 | VPN only |
| AdGuard Home | 3000 | VPN only |
| WireGuard VPN | 51820 | WAN allowed |

### **DNS Configuration**
- **Primary DNS**: ************* (AdGuard Home)
- **Upstream DNS**: *******, *******
- **DNS Rewrites**:
  - `*.mdewaele.freeddns.org` → *************
  - `router.mdewaele.freeddns.org` → ***********

## 🧪 **Testing Commands**

### **Network Connectivity**
```bash
# Test LAG performance
iperf3 -c ************* -t 30 -P 4

# Test VLAN isolation
ping -c 4 *************  # Should work from VPN
ping -c 4 **********     # Should work from guest to gateway only

# Test DNS resolution
nslookup home.mdewaele.freeddns.org *************
```

### **Security Verification**
```bash
# Test external blocking (should fail)
nmap -p 80,443,8080,8443,3000,5000,5001 your-external-ip

# Test VPN access (should work)
curl -k https://*************:8443  # Via VPN
```

### **Performance Testing**
```bash
# LAG status check
cat /proc/net/bonding/bond0  # On Synology

# Network throughput
iperf3 -s  # On NAS
iperf3 -c ************* -t 60 -P 8  # From client
```

## 🔍 **Troubleshooting Quick Reference**

### **Common Issues**
1. **LAG Not Working**:
   - Check USW Flex LAG configuration
   - Verify cable connections to ports 2-3
   - Confirm Synology bond interface active

2. **VPN Access Issues**:
   - Verify WireGuard server running
   - Check firewall rules allow 51820/UDP
   - Confirm client configuration correct

3. **DNS Resolution Problems**:
   - Check AdGuard Home running on port 3000
   - Verify DNS rewrites configured
   - Confirm EdgeRouter DNS forwarding

4. **Service Access Issues**:
   - Verify Traefik running on correct ports
   - Check firewall rules for internal access
   - Confirm service container status

### **Status Check Commands**
```bash
# EdgeRouter status
show interfaces
show dhcp leases
show firewall statistics

# Synology LAG status
cat /proc/net/bonding/bond0

# Service status
docker ps  # On Synology
systemctl status wireguard  # On EdgeRouter
```

## 📋 **Implementation Checklist Summary**

### **Pre-Implementation**
- [ ] Hardware inventory complete
- [ ] Cable requirements met
- [ ] Physical locations planned
- [ ] Power requirements verified

### **Hardware Phase**
- [ ] All devices physically connected
- [ ] Power-on sequence completed
- [ ] Basic connectivity verified
- [ ] Link lights all active

### **Configuration Phase**
- [ ] EdgeRouter configuration applied
- [ ] Synology LAG configured
- [ ] VLANs and security rules active
- [ ] VPN server operational

### **Services Phase**
- [ ] AdGuard Home running
- [ ] Traefik reverse proxy active
- [ ] DNS rewrites functional
- [ ] All services accessible via VPN

### **Testing Phase**
- [ ] Network connectivity verified
- [ ] Performance targets met
- [ ] Security rules validated
- [ ] All services functional

**🎯 Total Implementation Time: 8-15 hours depending on complexity and troubleshooting needs**

This quick reference provides the essential information needed to implement your professional-grade home network with VPN-only access and DS923+ dual NIC LAG configuration.
