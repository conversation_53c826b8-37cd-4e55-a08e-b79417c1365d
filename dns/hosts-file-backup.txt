# Backup hosts file entries for client devices
# Add these entries to client devices' hosts files as a backup DNS resolution method
# This ensures services are accessible even if D<PERSON> fails
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: ***********/24

# Location of hosts file:
# Windows: C:\Windows\System32\drivers\etc\hosts
# macOS/Linux: /etc/hosts

# Format: IP_ADDRESS domain.com
# All services point to Synology NAS (*************)

# Services hosted on Synology NAS via Traefik
************* home.mdewaele.freeddns.org
************* traefik.mdewaele.freeddns.org
************* adguard.mdewaele.freeddns.org

# EdgeRouter management
*********** router.mdewaele.freeddns.org

# Synology NAS direct access
************* nas.mdewaele.freeddns.org

# Additional services (all hosted on Synology NAS)
# ************* portainer.mdewaele.freeddns.org
# ************* plex.mdewaele.freeddns.org
# ************* nextcloud.mdewaele.freeddns.org

# Instructions:
# 1. Edit the hosts file with administrator/root privileges
# 2. Add the entries above
# 3. Save the file
# 4. Test by pinging the domains

# Windows PowerShell (run as Administrator):
# notepad C:\Windows\System32\drivers\etc\hosts

# macOS/Linux:
# sudo nano /etc/hosts

# Port Conflict Notes:
# - Synology NAS uses ports 80/443 for its web interface
# - Traefik uses ports 8080/8443 to avoid conflicts
# - If accessing services directly, use: http://domain:8080 or https://domain:8443
# - See traefik/port-conflict-resolution.md for complete solutions

# Note: This is a backup method. Primary DNS resolution should work through AdGuard Home
