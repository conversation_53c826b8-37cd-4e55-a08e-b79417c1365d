#!/bin/bash
# EdgeRouter 4 DDNS Configuration for Dynu
# Configures Dynamic DNS to keep mdewa<PERSON>.freeddns.org updated with current external IP

echo "=== EdgeRouter 4 DDNS Configuration for Dynu ==="
echo "Configuring Dynamic DNS for mdewaele.freeddns.org"
echo "Date: $(date)"
echo ""

echo "IMPORTANT: Run these commands on your EdgeRouter 4 via SSH"
echo ""

echo "Prerequisites:"
echo "1. Dynu account created at https://www.dynu.com/"
echo "2. Domain mdewaele.freeddns.org registered with Dynu"
echo "3. Dynu API credentials (username/password or API key)"
echo ""

echo "Network Configuration:"
echo "- External Interface: eth0 (WAN)"
echo "- Internal Networks: ***********/24, ***********/24, etc."
echo "- VPN Server: Port 51820/UDP (must be accessible externally)"
echo "- Domain: mdewaele.freeddns.org"
echo ""

echo "1. Configure DDNS service for Dynu:"
echo "configure"
echo ""

echo "# Set DDNS service provider to custom (Dynu uses custom API)"
echo "set service dns dynamic interface eth0 service dynu-ddns host-name mdewaele.freeddns.org"
echo "set service dns dynamic interface eth0 service dynu-ddns server api.dynu.com"
echo "set service dns dynamic interface eth0 service dynu-ddns protocol dynu"
echo ""

echo "# Configure authentication (replace with your actual credentials)"
echo "set service dns dynamic interface eth0 service dynu-ddns login YOUR_DYNU_USERNAME"
echo "set service dns dynamic interface eth0 service dynu-ddns password YOUR_DYNU_PASSWORD"
echo ""

echo "# Optional: Configure update options"
echo "set service dns dynamic interface eth0 service dynu-ddns options 'ssl=yes'"
echo "set service dns dynamic interface eth0 service dynu-ddns options 'use=web'"
echo "set service dns dynamic interface eth0 service dynu-ddns options 'web=checkip.dynu.com'"
echo ""

echo "# Set update interval (in seconds, default is 600 = 10 minutes)"
echo "set service dns dynamic interface eth0 service dynu-ddns options 'daemon=300'"
echo ""

echo "2. Alternative configuration using generic custom protocol:"
echo "# If the above doesn't work, use custom protocol"
echo ""

echo "# Remove previous configuration if needed"
echo "# delete service dns dynamic interface eth0 service dynu-ddns"
echo ""

echo "# Configure using custom protocol"
echo "set service dns dynamic interface eth0 service dynu-custom host-name mdewaele.freeddns.org"
echo "set service dns dynamic interface eth0 service dynu-custom server api.dynu.com"
echo "set service dns dynamic interface eth0 service dynu-custom protocol custom"
echo "set service dns dynamic interface eth0 service dynu-custom login YOUR_DYNU_USERNAME"
echo "set service dns dynamic interface eth0 service dynu-custom password YOUR_DYNU_PASSWORD"
echo ""

echo "# Custom update URL for Dynu API"
echo "set service dns dynamic interface eth0 service dynu-custom options 'url=https://api.dynu.com/nic/update?hostname=mdewaele.freeddns.org&myip='"
echo "set service dns dynamic interface eth0 service dynu-custom options 'ssl=yes'"
echo ""

echo "3. Configure using ddclient-style configuration (most reliable):"
echo "# This method uses the built-in ddclient with custom configuration"
echo ""

echo "# Remove any existing DDNS configuration"
echo "delete service dns dynamic"
echo ""

echo "# Configure ddclient for Dynu"
echo "set service dns dynamic interface eth0 service dynu host-name mdewaele.freeddns.org"
echo "set service dns dynamic interface eth0 service dynu server api.dynu.com"
echo "set service dns dynamic interface eth0 service dynu protocol dyndns2"
echo "set service dns dynamic interface eth0 service dynu login YOUR_DYNU_USERNAME"
echo "set service dns dynamic interface eth0 service dynu password YOUR_DYNU_PASSWORD"
echo ""

echo "# Advanced options for Dynu"
echo "set service dns dynamic interface eth0 service dynu options 'ssl=yes'"
echo "set service dns dynamic interface eth0 service dynu options 'use=web, web=checkip.dynu.com/'"
echo "set service dns dynamic interface eth0 service dynu options 'daemon=300'"
echo "set service dns dynamic interface eth0 service dynu options 'syslog=yes'"
echo ""

echo "4. Commit and save configuration:"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Verification and Testing ==="
echo ""

echo "1. Check DDNS service status:"
echo "show service dns dynamic status"
echo "show service dns dynamic"
echo ""

echo "2. Check system logs for DDNS updates:"
echo "show log tail 50 | grep -i ddns"
echo "show log tail 50 | grep -i dynu"
echo ""

echo "3. Force DDNS update (if needed):"
echo "update dns dynamic interface eth0"
echo ""

echo "4. Test external domain resolution:"
echo "nslookup mdewaele.freeddns.org *******"
echo "dig mdewaele.freeddns.org @*******"
echo ""

echo "5. Verify WireGuard VPN accessibility:"
echo "# From external network, test VPN connection using:"
echo "# mdewaele.freeddns.org:51820"
echo ""

echo "=== Troubleshooting ==="
echo ""

echo "Common Issues and Solutions:"
echo ""

echo "1. Authentication Failed:"
echo "   - Verify Dynu username/password are correct"
echo "   - Check if Dynu account requires API key instead of password"
echo "   - Try generating API key in Dynu control panel"
echo ""

echo "2. Update Failed:"
echo "   - Check internet connectivity: ping *******"
echo "   - Verify external interface is eth0: show interfaces"
echo "   - Check firewall allows outbound HTTPS: show firewall"
echo ""

echo "3. Domain Not Updating:"
echo "   - Verify domain is active in Dynu control panel"
echo "   - Check TTL settings (should be low, like 60-300 seconds)"
echo "   - Manually update once in Dynu panel to test"
echo ""

echo "4. Service Not Starting:"
echo "   - Check configuration syntax: show service dns dynamic"
echo "   - Restart DDNS service: restart dns dynamic"
echo "   - Check system resources: show system resources"
echo ""

echo "=== Advanced Configuration ==="
echo ""

echo "For multiple subdomains or advanced features:"
echo ""

echo "1. Configure multiple hostnames:"
echo "configure"
echo "set service dns dynamic interface eth0 service dynu-main host-name mdewaele.freeddns.org"
echo "set service dns dynamic interface eth0 service dynu-main server api.dynu.com"
echo "set service dns dynamic interface eth0 service dynu-main protocol dyndns2"
echo "set service dns dynamic interface eth0 service dynu-main login YOUR_USERNAME"
echo "set service dns dynamic interface eth0 service dynu-main password YOUR_PASSWORD"
echo ""

echo "# Add wildcard support (if supported by Dynu plan)"
echo "set service dns dynamic interface eth0 service dynu-wildcard host-name *.mdewaele.freeddns.org"
echo "set service dns dynamic interface eth0 service dynu-wildcard server api.dynu.com"
echo "set service dns dynamic interface eth0 service dynu-wildcard protocol dyndns2"
echo "set service dns dynamic interface eth0 service dynu-wildcard login YOUR_USERNAME"
echo "set service dns dynamic interface eth0 service dynu-wildcard password YOUR_PASSWORD"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "2. Custom update script (if built-in doesn't work):"
echo "# Create custom script at /config/scripts/dynu-update.sh"
echo "sudo mkdir -p /config/scripts"
echo "sudo tee /config/scripts/dynu-update.sh << 'EOF'"
echo "#!/bin/bash"
echo "# Custom Dynu DDNS Update Script"
echo ""
echo "USERNAME='YOUR_DYNU_USERNAME'"
echo "PASSWORD='YOUR_DYNU_PASSWORD'"
echo "HOSTNAME='mdewaele.freeddns.org'"
echo ""
echo "# Get current external IP"
echo "CURRENT_IP=\$(curl -s https://checkip.dynu.com/)"
echo ""
echo "# Update Dynu DNS"
echo "curl -s \"https://api.dynu.com/nic/update?hostname=\$HOSTNAME&myip=\$CURRENT_IP\" \\"
echo "     -u \"\$USERNAME:\$PASSWORD\""
echo ""
echo "echo \"Updated \$HOSTNAME to \$CURRENT_IP at \$(date)\""
echo "EOF"
echo ""

echo "# Make script executable"
echo "sudo chmod +x /config/scripts/dynu-update.sh"
echo ""

echo "# Add to cron for regular updates (every 5 minutes)"
echo "configure"
echo "set system task-scheduler task dynu-update executable path /config/scripts/dynu-update.sh"
echo "set system task-scheduler task dynu-update interval 5m"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Security Considerations ==="
echo ""

echo "1. Firewall Rules:"
echo "   - Ensure WireGuard port 51820/UDP is open"
echo "   - Block all other external access to internal services"
echo "   - DDNS only updates the main domain, not internal routing"
echo ""

echo "2. DNS Security:"
echo "   - Use strong Dynu account password"
echo "   - Enable 2FA on Dynu account if available"
echo "   - Regularly monitor DDNS update logs"
echo ""

echo "3. Network Security:"
echo "   - DDNS only provides external IP resolution"
echo "   - All services remain VPN-only accessible"
echo "   - Guest network remains isolated"
echo ""

echo "=== Integration with Existing Setup ==="
echo ""

echo "Your current configuration already includes:"
echo "- Domain: mdewaele.freeddns.org"
echo "- Internal DNS: AdGuard Home (*************)"
echo "- VPN Server: WireGuard on port 51820"
echo "- Services: All accessible via VPN only"
echo ""

echo "DDNS will ensure:"
echo "- External clients can find your VPN server"
echo "- Domain always points to current external IP"
echo "- No change to internal security model"
echo "- Continued VPN-only access to all services"
echo ""

echo "=== Final Notes ==="
echo ""
echo "Remember to:"
echo "1. Replace YOUR_DYNU_USERNAME and YOUR_DYNU_PASSWORD with actual credentials"
echo "2. Test the configuration after applying"
echo "3. Monitor logs for successful updates"
echo "4. Update WireGuard client configs to use mdewaele.freeddns.org:51820"
echo ""

echo "Configuration complete! Your domain will automatically update with your current external IP."
