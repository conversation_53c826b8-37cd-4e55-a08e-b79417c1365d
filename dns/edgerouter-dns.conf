# EdgeRouter DNS Configuration
# Configure DNS settings on EdgeRouter for proper resolution
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: 192.168.5.0/24

# Commands to run on EdgeRouter via SSH:

echo "=== EdgeRouter DNS Configuration ==="
echo ""

echo "1. Configure DNS forwarding to AdGuard Home:"
echo "configure"
echo "set service dns forwarding cache-size 1000"
echo "set service dns forwarding listen-on eth1"  # Wired LAN interface
echo "set service dns forwarding listen-on eth2"  # WiFi LAN interface (if separate)
echo "set service dns forwarding listen-on wg0"   # WireGuard interface
echo "set service dns forwarding name-server *************"  # AdGuard Home on Synology NAS
echo "set service dns forwarding options strict-order"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "2. Configure DHCP to use AdGuard Home as DNS:"
echo "configure"
echo "# Wired LAN DHCP"
echo "set service dhcp-server shared-network-name LAN subnet ***********/24 dns-server *************"
echo "# WiFi LAN DHCP (if managed by EdgeRouter)"
echo "set service dhcp-server shared-network-name WIFI subnet ***********/24 dns-server *************"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "3. Optional: Add local DNS entries on EdgeRouter (alternative to AdGuard rewrites):"
echo "configure"
echo "set system static-host-mapping host-name home.mdewaele.freeddns.org inet *************"
echo "set system static-host-mapping host-name traefik.mdewaele.freeddns.org inet *************"
echo "set system static-host-mapping host-name adguard.mdewaele.freeddns.org inet *************"
echo "set system static-host-mapping host-name router.mdewaele.freeddns.org inet ***********"
echo "set system static-host-mapping host-name nas.mdewaele.freeddns.org inet *************"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "4. Verify DNS configuration:"
echo "show service dns forwarding"
echo "show service dhcp-server"
echo "show system static-host-mapping"
