# Dynu DDNS Quick Setup for EdgeRouter 4

## Overview
Configure your EdgeRouter 4 to automatically update your `mdewaele.freeddns.org` domain with your current external IP address using Dynu's DDNS service.

## Prerequisites
- ✅ Dynu account at https://www.dynu.com/
- ✅ Domain `mdewaele.freeddns.org` registered with Dynu
- ✅ Dynu username and password (or API key)
- ✅ SSH access to EdgeRouter 4

## Quick Configuration

### Step 1: Get Your Dynu Credentials
1. Log into your Dynu control panel
2. Go to **DDNS Services** → **Control Panel**
3. Note your **Username** and **Password**
4. Optional: Generate an **API Key** for enhanced security

### Step 2: Configure EdgeRouter DDNS

SSH to your EdgeRouter and run these commands:

```bash
configure

# Configure DDNS for Dynu
set service dns dynamic interface eth0 service dynu host-name mdewaele.freeddns.org
set service dns dynamic interface eth0 service dynu server api.dynu.com
set service dns dynamic interface eth0 service dynu protocol dyndns2
set service dns dynamic interface eth0 service dynu login YOUR_DYNU_USERNAME
set service dns dynamic interface eth0 service dynu password YOUR_DYNU_PASSWORD

# Configure update options
set service dns dynamic interface eth0 service dynu options 'ssl=yes'
set service dns dynamic interface eth0 service dynu options 'use=web, web=checkip.dynu.com/'
set service dns dynamic interface eth0 service dynu options 'daemon=300'
set service dns dynamic interface eth0 service dynu options 'syslog=yes'

# Commit and save
commit
save
exit
```

**Important**: Replace `YOUR_DYNU_USERNAME` and `YOUR_DYNU_PASSWORD` with your actual Dynu credentials.

### Step 3: Verify Configuration

```bash
# Check DDNS status
show service dns dynamic status
show service dns dynamic

# Check logs
show log tail 20 | grep -i ddns

# Force an update
update dns dynamic interface eth0
```

### Step 4: Test External Resolution

```bash
# Test from EdgeRouter
nslookup mdewaele.freeddns.org *******

# Test from external network
dig mdewaele.freeddns.org @*******
```

## Alternative Configuration (If Above Doesn't Work)

Some EdgeRouter versions may require a different approach:

```bash
configure

# Remove existing configuration
delete service dns dynamic

# Use custom protocol
set service dns dynamic interface eth0 service dynu-custom host-name mdewaele.freeddns.org
set service dns dynamic interface eth0 service dynu-custom server api.dynu.com
set service dns dynamic interface eth0 service dynu-custom protocol custom
set service dns dynamic interface eth0 service dynu-custom login YOUR_DYNU_USERNAME
set service dns dynamic interface eth0 service dynu-custom password YOUR_DYNU_PASSWORD
set service dns dynamic interface eth0 service dynu-custom options 'url=https://api.dynu.com/nic/update?hostname=mdewaele.freeddns.org&myip='
set service dns dynamic interface eth0 service dynu-custom options 'ssl=yes'

commit
save
exit
```

## Integration with Your Current Setup

### WireGuard VPN Configuration
Update your WireGuard client configurations to use the DDNS domain:

**Before:**
```ini
[Peer]
Endpoint = YOUR_STATIC_IP:51820
```

**After:**
```ini
[Peer]
Endpoint = mdewaele.freeddns.org:51820
```

### Traefik Let's Encrypt
Your Traefik configuration already references Dynu for DNS challenges:

<augment_code_snippet path="traefik/traefik.yml" mode="EXCERPT">
```yaml
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      dnsChallenge:
        provider: dynu
        delayBeforeCheck: 30
```
</augment_code_snippet>

Make sure your Traefik environment includes your Dynu API key:

<augment_code_snippet path="traefik/docker-compose.yml" mode="EXCERPT">
```yaml
environment:
  - DYNU_API_KEY=your_dynu_api_key
```
</augment_code_snippet>

## Monitoring and Maintenance

### Check DDNS Updates
```bash
# View recent DDNS activity
show log tail 50 | grep -i ddns
show log tail 50 | grep -i dynu

# Check current external IP
curl -s https://checkip.dynu.com/

# Compare with DNS resolution
nslookup mdewaele.freeddns.org *******
```

### Troubleshooting Common Issues

#### 1. Authentication Failed
```bash
# Verify credentials in Dynu control panel
# Try using API key instead of password
set service dns dynamic interface eth0 service dynu password YOUR_API_KEY
```

#### 2. Updates Not Working
```bash
# Check internet connectivity
ping *******

# Verify external interface
show interfaces ethernet eth0

# Restart DDNS service
restart dns dynamic
```

#### 3. Domain Not Resolving
```bash
# Check Dynu control panel for domain status
# Verify TTL is set low (60-300 seconds)
# Test manual update in Dynu panel
```

## Security Considerations

### Firewall Configuration
Your current firewall setup should allow:
- ✅ Outbound HTTPS (port 443) for DDNS updates
- ✅ Inbound UDP 51820 for WireGuard VPN
- ❌ All other external access blocked

### Network Security
- DDNS only updates the external IP resolution
- All services remain VPN-only accessible
- Internal network security unchanged
- Guest network isolation maintained

## Advanced Options

### Multiple Subdomains
If you need to update multiple subdomains:

```bash
configure

# Main domain
set service dns dynamic interface eth0 service dynu-main host-name mdewaele.freeddns.org
set service dns dynamic interface eth0 service dynu-main server api.dynu.com
set service dns dynamic interface eth0 service dynu-main protocol dyndns2
set service dns dynamic interface eth0 service dynu-main login YOUR_USERNAME
set service dns dynamic interface eth0 service dynu-main password YOUR_PASSWORD

# Wildcard (if supported by your Dynu plan)
set service dns dynamic interface eth0 service dynu-wildcard host-name *.mdewaele.freeddns.org
set service dns dynamic interface eth0 service dynu-wildcard server api.dynu.com
set service dns dynamic interface eth0 service dynu-wildcard protocol dyndns2
set service dns dynamic interface eth0 service dynu-wildcard login YOUR_USERNAME
set service dns dynamic interface eth0 service dynu-wildcard password YOUR_PASSWORD

commit
save
exit
```

### Custom Update Script
For maximum reliability, you can create a custom update script:

```bash
# Create custom script
sudo mkdir -p /config/scripts
sudo nano /config/scripts/dynu-update.sh
```

Add this content:
```bash
#!/bin/bash
USERNAME='YOUR_DYNU_USERNAME'
PASSWORD='YOUR_DYNU_PASSWORD'
HOSTNAME='mdewaele.freeddns.org'

CURRENT_IP=$(curl -s https://checkip.dynu.com/)
curl -s "https://api.dynu.com/nic/update?hostname=$HOSTNAME&myip=$CURRENT_IP" \
     -u "$USERNAME:$PASSWORD"

echo "Updated $HOSTNAME to $CURRENT_IP at $(date)" >> /var/log/dynu-ddns.log
```

Make executable and schedule:
```bash
sudo chmod +x /config/scripts/dynu-update.sh

configure
set system task-scheduler task dynu-update executable path /config/scripts/dynu-update.sh
set system task-scheduler task dynu-update interval 5m
commit
save
exit
```

## Verification Checklist

- [ ] DDNS service configured and running
- [ ] Domain resolves to current external IP
- [ ] WireGuard VPN accessible via domain name
- [ ] Logs show successful DDNS updates
- [ ] Traefik SSL certificates working with DNS challenge
- [ ] All services remain VPN-only accessible

## Next Steps

1. **Apply the configuration** using the commands above
2. **Test VPN connectivity** using the domain name
3. **Update client configurations** to use the domain instead of IP
4. **Monitor logs** for successful DDNS updates
5. **Test failover** by checking updates after IP changes

Your `mdewaele.freeddns.org` domain will now automatically stay updated with your current external IP, ensuring reliable VPN access and SSL certificate renewals!
