# AdGuard Home Custom DNS Configuration - COMPLETE SERVICE INVENTORY
# Add these entries to AdGuard Home -> Filters -> DNS rewrites
# This ensures your subdomains resolve to internal IPs when connected via VPN
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - Guest Network: **********/24 (BLOCKED from accessing these services)
# - VPN Network: ***********/24
#
# IMPORTANT: Port Conflict Resolution
# Synology NAS uses ports 80/443. <PERSON><PERSON><PERSON><PERSON> uses ports 8080/8443.
# See traefik/port-conflict-resolution.md for detailed solutions.
#
# SECURITY: Guest network (**********/24) cannot access any of these services

# Format: domain.com IP_ADDRESS
# All services point to Synology NAS IP (*************)

# ===== CORE INFRASTRUCTURE SERVICES =====
# All containerized services point to Synology NAS (*************)
# Traefi<PERSON> will handle internal routing to containers

# Main Home Dashboard
home.mdewaele.freeddns.org *************

# Reverse Proxy & Load Balancer
traefik.mdewaele.freeddns.org *************
lb.mdewaele.freeddns.org *************

# DNS & Network Services
adguard.mdewaele.freeddns.org *************
router.mdewaele.freeddns.org ***********

# Container Management
portainer.mdewaele.freeddns.org *************

# NAS Management
nas.mdewaele.freeddns.org *************

# ===== HOME AUTOMATION & IOT SERVICES =====
# Home Assistant - Smart Home Controller
home.mdewaele.freeddns.org *************

# Zigbee Controller - IoT Device Management
zigbee.mdewaele.freeddns.org *************

# UniFi Network Controller - Network Management
unifi.mdewaele.freeddns.org *************

# UniFi Hotspot Manager - Guest Network Management
hotspot.mdewaele.freeddns.org *************

# ===== MEDIA & ENTERTAINMENT SERVICES =====
# Jellyfin Media Server - Movies, TV Shows, Music
jellyfin.mdewaele.freeddns.org *************

# Custom Media Web Application
media.mdewaele.freeddns.org *************

# Media Web App API (PHP Backend)
api.media.mdewaele.freeddns.org *************

# ===== STATIC DEVICE MAPPINGS =====
# Yamaha MusicCast Speakers - Update IPs to match your actual devices
# These should point to the actual IP addresses of your speakers

# Living Room Speaker
livingroom.mdewaele.freeddns.org 192.168.1.XXX  # Replace XXX with actual IP

# Kitchen Speaker
kitchen.mdewaele.freeddns.org 192.168.1.XXX     # Replace XXX with actual IP

# Bureau/Office Speaker
bureau.mdewaele.freeddns.org 192.168.1.XXX      # Replace XXX with actual IP

# Bedroom Speaker
bedroom.mdewaele.freeddns.org 192.168.1.XXX     # Replace XXX with actual IP

# ===== CONFIGURATION INSTRUCTIONS =====
# 1. Access AdGuard Home web interface: http://*************:3000
# 2. Go to Filters -> DNS rewrites
# 3. Add each entry above in the format: domain.com -> IP_ADDRESS
# 4. For static devices (speakers), replace XXX with actual IP addresses
# 5. Save configuration
# 6. Test DNS resolution from VPN-connected device

# ===== SECURITY NOTES =====
# - All services are accessible ONLY via VPN (***********/24) or internal networks
# - Guest network (**********/24) is BLOCKED from accessing any of these services
# - External access is BLOCKED by EdgeRouter firewall rules
# - Traefik handles SSL/TLS termination and internal routing for containerized services
# plex.mdewaele.freeddns.org *************
# nextcloud.mdewaele.freeddns.org *************

# Instructions for AdGuard Home:
# 1. Access AdGuard Home web interface at http://*************:3000
# 2. Go to Filters -> DNS rewrites
# 3. Add each entry above as a new DNS rewrite rule
# 4. Format: Domain = subdomain.mdewaele.freeddns.org, Answer = *************
# 5. Save and apply changes

# Port Conflict Notes:
# - DNS rewrites point to ************* (Synology NAS)
# - Traefik runs on ports 8080/8443 to avoid NAS port conflicts
# - See traefik/port-conflict-resolution.md for solutions:
#   * Option 1: Access services with port numbers (e.g., :8080, :8443)
#   * Option 2: Configure Nginx reverse proxy on NAS (recommended)
#   * Option 3: Use secondary IP address for Traefik

# Alternative: Use wildcard entry (less secure but simpler)
# *.mdewaele.freeddns.org *************
