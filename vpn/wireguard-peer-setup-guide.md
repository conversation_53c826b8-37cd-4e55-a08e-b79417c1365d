# WireGuard Peer Configuration Guide - EdgeRouter 4 & Mobile

## Overview
Complete step-by-step guide to configure WireGuard peers on EdgeRouter 4 server and mobile clients for secure VPN access to your home network.

## Network Configuration
- **VPN Network**: ***********/24
- **Server IP**: *********** (EdgeRouter 4)
- **Client IPs**: ***********-254
- **VPN Port**: 51820/UDP
- **Domain**: mdewaele.freeddns.org

## Prerequisites
- ✅ EdgeRouter 4 with WireGuard server configured
- ✅ DDNS configured (mdewaele.freeddns.org)
- ✅ Firewall allowing port 51820/UDP
- ✅ Mobile device with WireGuard app installed

## Part 1: EdgeRouter 4 Server Configuration

### Step 1: Generate Server Keys (If Not Done)

SSH to your EdgeRouter 4:

```bash
# Create directory for WireGuard keys
sudo mkdir -p /config/auth/wireguard
cd /config/auth/wireguard

# Generate server keys
wg genkey | tee server-private.key | wg pubkey > server-public.key
chmod 600 server-private.key
chmod 644 server-public.key

# Display server public key (needed for clients)
cat server-public.key
```

### Step 2: Configure WireGuard Interface

```bash
configure

# Configure WireGuard interface
set interfaces wireguard wg0 address ***********/24
set interfaces wireguard wg0 description 'WireGuard VPN Server'
set interfaces wireguard wg0 listen-port 51820
set interfaces wireguard wg0 private-key /config/auth/wireguard/server-private.key

# Configure routing
set protocols static route ***********/24 next-hop-interface wg0

# Configure NAT for VPN clients
set service nat rule 100 description 'VPN clients to internet'
set service nat rule 100 outbound-interface eth0
set service nat rule 100 source address ***********/24
set service nat rule 100 type masquerade

commit
save
exit
```

### Step 3: Configure Firewall Rules

```bash
configure

# Allow WireGuard traffic from WAN
set firewall name WAN_IN rule 10 action accept
set firewall name WAN_IN rule 10 description 'Allow WireGuard VPN'
set firewall name WAN_IN rule 10 destination port 51820
set firewall name WAN_IN rule 10 protocol udp

set firewall name WAN_LOCAL rule 10 action accept
set firewall name WAN_LOCAL rule 10 description 'Allow WireGuard VPN'
set firewall name WAN_LOCAL rule 10 destination port 51820
set firewall name WAN_LOCAL rule 10 protocol udp

# Apply firewall rules
set interfaces ethernet eth0 firewall in name WAN_IN
set interfaces ethernet eth0 firewall local name WAN_LOCAL

commit
save
exit
```

## Part 2: Generate Client Keys and Configuration

### Step 4: Generate Client Keys

On your computer or EdgeRouter, generate keys for each client:

```bash
# Generate client private key
CLIENT_PRIVATE_KEY=$(wg genkey)

# Generate client public key
CLIENT_PUBLIC_KEY=$(echo "$CLIENT_PRIVATE_KEY" | wg pubkey)

echo "Client Private Key: $CLIENT_PRIVATE_KEY"
echo "Client Public Key: $CLIENT_PUBLIC_KEY"
```

### Step 5: Add Client Peer to EdgeRouter

For each client, add a peer configuration:

```bash
configure

# Add client peer (replace with actual public key and IP)
set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY allowed-ips ***********/32
set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY description 'Mobile Phone'

# For additional clients, use different IPs:
# ***********/32 for tablet
# ***********/32 for laptop
# etc.

commit
save
exit
```

## Part 3: Mobile Client Configuration

### Step 6: Create Mobile Client Configuration

Create a configuration file for your mobile device:

```ini
[Interface]
# Client's private key (generated above)
PrivateKey = CLIENT_PRIVATE_KEY

# Client's VPN IP address
Address = ***********/24

# DNS servers - use AdGuard Home for ad blocking
DNS = *************

[Peer]
# Server's public key (from EdgeRouter)
PublicKey = SERVER_PUBLIC_KEY

# Server endpoint - your DDNS domain
Endpoint = mdewaele.freeddns.org:51820

# Allowed IPs - route only local networks through VPN
AllowedIPs = ***********/24, ***********/24, ***********/24, ************/24

# Keep connection alive (important for mobile)
PersistentKeepalive = 25
```

### Step 7: Install and Configure Mobile App

#### For Android:
1. Install **WireGuard** from Google Play Store
2. Tap **+** → **Create from file or archive**
3. Import the configuration file or scan QR code
4. Toggle the connection on

#### For iOS:
1. Install **WireGuard** from App Store
2. Tap **+** → **Create from file or archive**
3. Import the configuration file or scan QR code
4. Toggle the connection on

## Part 4: Advanced Configuration Options

### Option 1: Route All Traffic Through VPN

For maximum security, route all traffic through VPN:

```ini
[Interface]
PrivateKey = CLIENT_PRIVATE_KEY
Address = ***********/24
DNS = *************

[Peer]
PublicKey = SERVER_PUBLIC_KEY
Endpoint = mdewaele.freeddns.org:51820
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
```

### Option 2: Split Tunneling (Local Networks Only)

Route only local networks through VPN (saves mobile data):

```ini
[Interface]
PrivateKey = CLIENT_PRIVATE_KEY
Address = ***********/24
DNS = *************

[Peer]
PublicKey = SERVER_PUBLIC_KEY
Endpoint = mdewaele.freeddns.org:51820
AllowedIPs = ***********/24, ***********/24, ***********/24, ************/24
PersistentKeepalive = 25
```

## Part 5: Testing and Verification

### Step 8: Test VPN Connection

After configuring the mobile client:

1. **Connect to VPN** on mobile device
2. **Check IP assignment**: Should get 192.168.5.x
3. **Test internal access**: 
   - Browse to `http://*************:3000` (AdGuard)
   - Browse to `http://*************:8080` (Traefik)
4. **Test DNS resolution**:
   - `nslookup home.mdewaele.freeddns.org`
   - Should resolve to *************

### Step 9: Verify EdgeRouter Status

On EdgeRouter, check WireGuard status:

```bash
# Show WireGuard interface status
show interfaces wireguard wg0

# Show active peers
sudo wg show

# Check VPN client connectivity
ping ***********

# Monitor logs
show log tail 20 | grep wireguard
```

## Part 6: Multiple Client Setup

### Step 10: Add Additional Clients

For each additional device, repeat the process with unique IPs:

```bash
# Client 2 (Tablet)
configure
set interfaces wireguard wg0 peer CLIENT2_PUBLIC_KEY allowed-ips ***********/32
set interfaces wireguard wg0 peer CLIENT2_PUBLIC_KEY description 'Tablet'
commit
save
exit

# Client 3 (Laptop)
configure
set interfaces wireguard wg0 peer CLIENT3_PUBLIC_KEY allowed-ips ***********/32
set interfaces wireguard wg0 peer CLIENT3_PUBLIC_KEY description 'Laptop'
commit
save
exit
```

## Troubleshooting

### Common Issues:

1. **Connection fails**:
   - Check DDNS resolution: `nslookup mdewaele.freeddns.org`
   - Verify port 51820 is open: `telnet mdewaele.freeddns.org 51820`
   - Check EdgeRouter firewall logs

2. **Can't access internal services**:
   - Verify AllowedIPs includes internal networks
   - Check DNS settings point to *************
   - Test direct IP access first

3. **Peer not connecting**:
   - Verify public keys match exactly
   - Check IP address conflicts
   - Ensure unique IPs for each client

### Useful Commands:

```bash
# EdgeRouter diagnostics
show interfaces wireguard wg0
sudo wg show
show log tail 50 | grep -i wireguard

# Client diagnostics (on mobile, if available)
# Check assigned IP, DNS, and routing table
```

## Security Best Practices

1. **Key Management**:
   - Keep private keys secure
   - Use unique keys for each client
   - Regularly rotate keys if compromised

2. **Network Access**:
   - Use split tunneling for mobile data savings
   - Monitor VPN usage in EdgeRouter logs
   - Regularly review connected clients

3. **Firewall Rules**:
   - Only allow necessary ports
   - Monitor failed connection attempts
   - Keep firmware updated

## Quick Reference

### IP Assignments:
- Server: ***********
- Mobile Phone: ***********
- Tablet: ***********
- Laptop: ***********
- Additional clients: ***********-254

### Key Files:
- Server private key: `/config/auth/wireguard/server-private.key`
- Server public key: `/config/auth/wireguard/server-public.key`
- Client configs: Store securely on each device

### Access URLs (via VPN):
- AdGuard Home: `http://*************:3000`
- Traefik Dashboard: `http://*************:9080`
- Synology DSM: `http://*************:5000`
- Home Assistant: `http://*************:8123`

Your WireGuard VPN is now configured for secure remote access to your home network!
