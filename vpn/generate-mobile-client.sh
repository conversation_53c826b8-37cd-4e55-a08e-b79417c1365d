#!/bin/bash
# WireGuard Mobile Client Configuration Generator
# Generates client configurations and EdgeRouter peer commands

echo "=== WireGuard Mobile Client Configuration Generator ==="
echo "This script generates WireGuard client configurations for mobile devices"
echo "Date: $(date)"
echo ""

# Configuration variables
SERVER_ENDPOINT="mdewaele.freeddns.org:51820"
VPN_NETWORK="***********/24"
SERVER_IP="***********"
DNS_SERVER="*************"  # AdGuard Home on Synology NAS

# Internal networks accessible via VPN
ALLOWED_IPS_SPLIT="***********/24, ***********/24, ***********/24, ************/24"
ALLOWED_IPS_FULL="0.0.0.0/0"

# Color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "Network Configuration:"
echo "- VPN Network: $VPN_NETWORK"
echo "- Server: $SERVER_IP"
echo "- Endpoint: $SERVER_ENDPOINT"
echo "- DNS: $DNS_SERVER (AdGuard Home)"
echo ""

# Function to generate client keys
generate_keys() {
    local private_key=$(wg genkey)
    local public_key=$(echo "$private_key" | wg pubkey)
    
    echo "$private_key|$public_key"
}

# Function to get server public key
get_server_public_key() {
    echo "To get your server public key, run this command on your EdgeRouter:"
    echo -e "${YELLOW}cat /config/auth/wireguard/server-public.key${NC}"
    echo ""
    read -p "Enter your EdgeRouter server public key: " server_public_key
    echo "$server_public_key"
}

# Function to generate client configuration
generate_client_config() {
    local client_name="$1"
    local client_ip="$2"
    local client_private_key="$3"
    local client_public_key="$4"
    local server_public_key="$5"
    local tunnel_mode="$6"
    
    local allowed_ips
    if [ "$tunnel_mode" = "full" ]; then
        allowed_ips="$ALLOWED_IPS_FULL"
    else
        allowed_ips="$ALLOWED_IPS_SPLIT"
    fi
    
    local config_file="${client_name}.conf"
    
    cat > "$config_file" << EOF
# WireGuard Client Configuration - $client_name
# Generated on $(date)
# Network: Home Network VPN Access

[Interface]
# Client private key
PrivateKey = $client_private_key

# Client VPN IP address
Address = $client_ip/24

# DNS server - AdGuard Home for ad blocking and local resolution
DNS = $DNS_SERVER

[Peer]
# Server public key
PublicKey = $server_public_key

# Server endpoint - DDNS domain
Endpoint = $SERVER_ENDPOINT

# Allowed IPs - networks routed through VPN
AllowedIPs = $allowed_ips

# Keep connection alive (important for mobile)
PersistentKeepalive = 25
EOF

    echo -e "${GREEN}✓${NC} Configuration saved to: $config_file"
    return 0
}

# Function to generate QR code (if qrencode is available)
generate_qr_code() {
    local config_file="$1"
    
    if command -v qrencode >/dev/null 2>&1; then
        local qr_file="${config_file%.conf}.png"
        qrencode -t png -r "$config_file" -o "$qr_file"
        echo -e "${GREEN}✓${NC} QR code saved to: $qr_file"
    else
        echo -e "${YELLOW}⚠${NC} qrencode not available. Install with: sudo apt install qrencode"
    fi
}

# Function to display EdgeRouter commands
show_edgerouter_commands() {
    local client_name="$1"
    local client_ip="$2"
    local client_public_key="$3"
    
    echo ""
    echo -e "${BLUE}=== EdgeRouter Commands for $client_name ===${NC}"
    echo "Run these commands on your EdgeRouter 4 via SSH:"
    echo ""
    echo "configure"
    echo "set interfaces wireguard wg0 peer $client_public_key allowed-ips $client_ip/32"
    echo "set interfaces wireguard wg0 peer $client_public_key description '$client_name'"
    echo "commit"
    echo "save"
    echo "exit"
    echo ""
}

# Main script execution
main() {
    echo "Getting server public key..."
    server_public_key=$(get_server_public_key)
    
    if [ -z "$server_public_key" ]; then
        echo "Error: Server public key is required"
        exit 1
    fi
    
    echo ""
    echo "Client configuration options:"
    echo "1. Split tunneling (recommended) - Only route local networks through VPN"
    echo "2. Full tunneling - Route all traffic through VPN"
    echo ""
    read -p "Choose tunneling mode (1 or 2): " tunnel_choice
    
    case $tunnel_choice in
        1)
            tunnel_mode="split"
            echo "Selected: Split tunneling (local networks only)"
            ;;
        2)
            tunnel_mode="full"
            echo "Selected: Full tunneling (all traffic)"
            ;;
        *)
            tunnel_mode="split"
            echo "Invalid choice. Defaulting to split tunneling."
            ;;
    esac
    
    echo ""
    echo "Generating client configurations..."
    echo ""
    
    # Predefined clients with IP assignments
    clients=(
        "mobile-phone:***********"
        "tablet:***********"
        "laptop:***********"
    )
    
    # Ask which clients to generate
    echo "Available client configurations:"
    for i in "${!clients[@]}"; do
        IFS=':' read -r name ip <<< "${clients[$i]}"
        echo "$((i+1)). $name ($ip)"
    done
    echo "$((${#clients[@]}+1)). Custom client"
    echo ""
    
    read -p "Select clients to generate (e.g., 1,2 or 'all'): " selection
    
    if [ "$selection" = "all" ]; then
        selected_clients=("${clients[@]}")
    else
        selected_clients=()
        IFS=',' read -ra ADDR <<< "$selection"
        for i in "${ADDR[@]}"; do
            if [ "$i" -le "${#clients[@]}" ] && [ "$i" -gt 0 ]; then
                selected_clients+=("${clients[$((i-1))]}")
            elif [ "$i" -eq "$((${#clients[@]}+1))" ]; then
                # Custom client
                read -p "Enter custom client name: " custom_name
                read -p "Enter custom client IP (192.168.5.x): " custom_ip
                selected_clients+=("$custom_name:$custom_ip")
            fi
        done
    fi
    
    # Generate configurations for selected clients
    for client in "${selected_clients[@]}"; do
        IFS=':' read -r client_name client_ip <<< "$client"
        
        echo ""
        echo -e "${BLUE}Generating configuration for: $client_name${NC}"
        
        # Generate client keys
        keys=$(generate_keys)
        IFS='|' read -r client_private_key client_public_key <<< "$keys"
        
        # Generate configuration file
        generate_client_config "$client_name" "$client_ip" "$client_private_key" "$client_public_key" "$server_public_key" "$tunnel_mode"
        
        # Generate QR code if possible
        generate_qr_code "${client_name}.conf"
        
        # Show EdgeRouter commands
        show_edgerouter_commands "$client_name" "$client_ip" "$client_public_key"
        
        echo "Client Details:"
        echo "- Name: $client_name"
        echo "- VPN IP: $client_ip"
        echo "- Private Key: $client_private_key"
        echo "- Public Key: $client_public_key"
        echo ""
    done
    
    echo -e "${GREEN}=== Generation Complete ===${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Add each peer to your EdgeRouter using the commands shown above"
    echo "2. Import the .conf files into your WireGuard mobile apps"
    echo "3. Test the connections"
    echo ""
    echo "Mobile app installation:"
    echo "- Android: Install 'WireGuard' from Google Play Store"
    echo "- iOS: Install 'WireGuard' from App Store"
    echo ""
    echo "Import methods:"
    echo "- Scan QR code (if generated)"
    echo "- Import .conf file"
    echo "- Manual entry using the configuration details"
    echo ""
    
    # Show verification commands
    echo -e "${BLUE}=== Verification Commands ===${NC}"
    echo ""
    echo "On EdgeRouter (check status):"
    echo "show interfaces wireguard wg0"
    echo "sudo wg show"
    echo ""
    echo "Test connectivity (after client connects):"
    for client in "${selected_clients[@]}"; do
        IFS=':' read -r client_name client_ip <<< "$client"
        echo "ping $client_ip  # Test $client_name"
    done
    echo ""
    echo "Monitor logs:"
    echo "show log tail 20 | grep -i wireguard"
}

# Check if WireGuard tools are available
if ! command -v wg >/dev/null 2>&1; then
    echo "Error: WireGuard tools not found. Please install WireGuard first."
    echo "Ubuntu/Debian: sudo apt install wireguard-tools"
    echo "macOS: brew install wireguard-tools"
    exit 1
fi

# Run main function
main

echo ""
echo "Configuration files generated in current directory:"
ls -la *.conf 2>/dev/null || echo "No configuration files found"

if command -v qrencode >/dev/null 2>&1; then
    echo ""
    echo "QR code files generated:"
    ls -la *.png 2>/dev/null || echo "No QR code files found"
fi
