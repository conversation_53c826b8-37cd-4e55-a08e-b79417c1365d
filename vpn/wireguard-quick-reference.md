# WireGuard Quick Reference - EdgeRouter 4 & Mobile

## 🚀 Quick Setup Commands

### 1. Get Server Public Key (EdgeRouter)
```bash
cat /config/auth/wireguard/server-public.key
```

### 2. Generate Client Keys (Any Linux/Mac)
```bash
# Generate private key
wg genkey

# Generate public key from private key
echo "PRIVATE_KEY_HERE" | wg pubkey
```

### 3. Add Client to EdgeRouter
```bash
configure
set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY allowed-ips ***********/32
set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY description 'Mobile Phone'
commit
save
exit
```

## 📱 Mobile Client Configuration Template

### Split Tunneling (Recommended)
```ini
[Interface]
PrivateKey = CLIENT_PRIVATE_KEY
Address = ***********/24
DNS = *************

[Peer]
PublicKey = SERVER_PUBLIC_KEY
Endpoint = mdewaele.freeddns.org:51820
AllowedIPs = ***********/24, ***********/24, ***********/24, ************/24
PersistentKeepalive = 25
```

### Full Tunneling (All Traffic)
```ini
[Interface]
PrivateKey = CLIENT_PRIVATE_KEY
Address = ***********/24
DNS = *************

[Peer]
PublicKey = SERVER_PUBLIC_KEY
Endpoint = mdewaele.freeddns.org:51820
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
```

## 🔧 Management Commands

### EdgeRouter Status
```bash
# Show WireGuard interface
show interfaces wireguard wg0

# Show active peers
sudo wg show

# Show configuration
show interfaces wireguard

# Monitor logs
show log tail 20 | grep -i wireguard
```

### Add/Remove Peers
```bash
# Add new peer
configure
set interfaces wireguard wg0 peer NEW_PUBLIC_KEY allowed-ips 192.168.5.X/32
set interfaces wireguard wg0 peer NEW_PUBLIC_KEY description 'Device Name'
commit
save
exit

# Remove peer
configure
delete interfaces wireguard wg0 peer OLD_PUBLIC_KEY
commit
save
exit
```

## 📋 IP Address Assignments

| Device | IP Address | Description |
|--------|------------|-------------|
| Server | *********** | EdgeRouter 4 |
| Mobile Phone | *********** | Primary mobile |
| Tablet | *********** | Secondary mobile |
| Laptop | *********** | Portable computer |
| Desktop | *********** | Home computer |
| Available | ***********-254 | Additional clients |

## 🌐 Network Access via VPN

### Internal Services (via VPN)
- **AdGuard Home**: http://*************:3000
- **Traefik Dashboard**: http://*************:9080
- **Synology DSM**: http://*************:5000
- **Home Assistant**: http://*************:8123
- **Jellyfin**: http://*************:8096
- **Portainer**: http://*************:9000

### Domain Access (via VPN)
- **Home Dashboard**: http://home.mdewaele.freeddns.org
- **AdGuard**: http://adguard.mdewaele.freeddns.org
- **Traefik**: http://traefik.mdewaele.freeddns.org
- **NAS**: http://nas.mdewaele.freeddns.org

## 🔍 Troubleshooting

### Connection Issues
```bash
# Check DDNS resolution
nslookup mdewaele.freeddns.org

# Test port connectivity
telnet mdewaele.freeddns.org 51820

# Check EdgeRouter firewall
show firewall name WAN_IN
show firewall name WAN_LOCAL
```

### Peer Issues
```bash
# List all peers
sudo wg show wg0 peers

# Check peer handshakes
sudo wg show wg0 latest-handshakes

# Monitor peer traffic
sudo wg show wg0 transfer
```

### Network Issues
```bash
# Test VPN client connectivity
ping ***********

# Check routing
show ip route

# Test internal network access
ping *************
```

## 📱 Mobile App Setup

### Android (WireGuard App)
1. Install from Google Play Store
2. Tap **+** → **Create from file or archive**
3. Import `.conf` file or scan QR code
4. Toggle connection on

### iOS (WireGuard App)
1. Install from App Store
2. Tap **+** → **Create from file or archive**
3. Import `.conf` file or scan QR code
4. Toggle connection on

## ⚡ Automated Setup

### Generate Client Configs
```bash
# Use the automated script
bash vpn/generate-mobile-client.sh
```

### Bulk Client Setup
```bash
# Generate multiple clients at once
bash clients/generate-client-keys.sh
```

## 🛡️ Security Best Practices

### Key Management
- ✅ Use unique keys for each client
- ✅ Store private keys securely
- ✅ Regularly rotate compromised keys
- ❌ Never share private keys

### Network Security
- ✅ Use split tunneling for mobile data savings
- ✅ Monitor connected clients regularly
- ✅ Review VPN logs periodically
- ✅ Keep EdgeRouter firmware updated

### Access Control
- ✅ Only allow necessary internal networks
- ✅ Use descriptive peer names
- ✅ Remove unused peers promptly
- ✅ Monitor for unauthorized access

## 🔄 Common Maintenance Tasks

### Weekly
- Check connected clients: `sudo wg show`
- Review VPN logs: `show log | grep wireguard`

### Monthly
- Update mobile apps
- Check DDNS resolution
- Review peer descriptions and usage

### As Needed
- Add new clients
- Remove old/unused clients
- Update client configurations
- Troubleshoot connection issues

## 📞 Quick Support

### Key Information to Have Ready
- Server public key
- Client public key (for troubleshooting)
- Assigned IP addresses
- Error messages from logs
- Mobile app version

### Useful Log Commands
```bash
# Recent WireGuard activity
show log tail 50 | grep -i wireguard

# Firewall blocks
show log tail 50 | grep -i drop

# DDNS updates
show log tail 50 | grep -i ddns
```

---

**Remember**: Replace `CLIENT_PRIVATE_KEY`, `CLIENT_PUBLIC_KEY`, and `SERVER_PUBLIC_KEY` with actual generated keys!
