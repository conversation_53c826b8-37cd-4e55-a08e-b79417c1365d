#!/bin/bash
# Comprehensive test script for VPN-only access setup
# This script performs various tests to ensure the setup is working correctly
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - Guest Network: **********/24 (ISOLATED - Internet only)
# - VPN Network: ***********/24

echo "=== Comprehensive VPN-Only Access Test Suite ==="
echo "This script will test your complete VPN-only setup"
echo ""
echo "Network Configuration:"
echo "- Synology NAS: ************* (hosting all services)"
echo "- Traefik Ports: 8080 (HTTP), 8443 (HTTPS), 9080 (Dashboard)"
echo "- AdGuard Home: Port 3000"
echo "- Guest Network: **********/24 (ISOLATED from all internal services)"
echo ""

# Configuration - Updated for actual network topology
EXTERNAL_IP="YOUR_EXTERNAL_IP"  # Replace with your actual external IP
DOMAINS=(
    "home.mdewaele.freeddns.org"
    "traefik.mdewaele.freeddns.org"
    "adguard.mdewaele.freeddns.org"
    "router.mdewaele.freeddns.org"
)
SYNOLOGY_IP="*************"  # Synology NAS hosting all services
ADGUARD_IP="*************"   # AdGuard Home on Synology NAS
TRAEFIK_IP="*************"   # Traefik on Synology NAS

# Test counters
total_tests=0
passed_tests=0
failed_tests=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo "Running: $test_name"
    ((total_tests++))
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo "✅ PASS: $test_name"
        ((passed_tests++))
    else
        echo "❌ FAIL: $test_name"
        ((failed_tests++))
    fi
}

# Function to check if running on VPN
check_vpn_status() {
    if ip addr show | grep -q "192.168.5."; then
        echo "🔒 VPN Status: Connected"
        return 0
    else
        echo "🌐 VPN Status: Not connected (testing external access)"
        return 1
    fi
}

# Function to test external blocking
test_external_blocking() {
    echo ""
    echo "=== Testing External Access Blocking ==="
    
    for domain in "${DOMAINS[@]}"; do
        # Test HTTP blocking
        run_test "Block HTTP access to $domain" \
            "! curl -s --connect-timeout 5 --max-time 10 http://$domain >/dev/null 2>&1"
        
        # Test HTTPS blocking
        run_test "Block HTTPS access to $domain" \
            "! curl -s --connect-timeout 5 --max-time 10 https://$domain >/dev/null 2>&1"
    done
    
    # Test direct IP blocking for standard ports
    run_test "Block HTTP on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 80 2>/dev/null"

    run_test "Block HTTPS on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 443 2>/dev/null"

    # Test Traefik alternative ports blocking
    run_test "Block Traefik HTTP on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 8080 2>/dev/null"

    run_test "Block Traefik HTTPS on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 8443 2>/dev/null"

    run_test "Block Traefik Dashboard on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 9080 2>/dev/null"

    # Test other service ports blocking
    run_test "Block AdGuard Home on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 3000 2>/dev/null"

    run_test "Block Synology DSM HTTP on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 5000 2>/dev/null"

    run_test "Block Synology DSM HTTPS on external IP" \
        "! nc -z -w5 $EXTERNAL_IP 5001 2>/dev/null"

    # WireGuard port should be accessible
    run_test "WireGuard port accessible" \
        "nc -z -w5 $EXTERNAL_IP 51820 2>/dev/null"
}

# Function to test VPN access
test_vpn_access() {
    echo ""
    echo "=== Testing VPN Access ==="
    
    # Test VPN connectivity
    run_test "VPN gateway reachable" \
        "ping -c 3 -W 5 *********** >/dev/null 2>&1"

    run_test "Wired LAN gateway reachable via VPN" \
        "ping -c 3 -W 5 *********** >/dev/null 2>&1"

    run_test "Synology NAS reachable via VPN" \
        "ping -c 3 -W 5 $SYNOLOGY_IP >/dev/null 2>&1"
    
    # Test DNS resolution
    for domain in "${DOMAINS[@]}"; do
        run_test "DNS resolution for $domain" \
            "nslookup $domain >/dev/null 2>&1"
    done
    
    # Test web access via VPN
    for domain in "${DOMAINS[@]}"; do
        run_test "HTTP access to $domain via VPN" \
            "curl -s --connect-timeout 10 --max-time 15 http://$domain >/dev/null 2>&1"
        
        run_test "HTTPS access to $domain via VPN" \
            "curl -s --connect-timeout 10 --max-time 15 https://$domain >/dev/null 2>&1"
    done
    
    # Test direct service access
    run_test "AdGuard Home accessible" \
        "nc -z -w5 $ADGUARD_IP 3000 2>/dev/null"

    run_test "Traefik HTTP accessible" \
        "nc -z -w5 $TRAEFIK_IP 8080 2>/dev/null"

    run_test "Traefik HTTPS accessible" \
        "nc -z -w5 $TRAEFIK_IP 8443 2>/dev/null"

    run_test "Traefik Dashboard accessible" \
        "nc -z -w5 $TRAEFIK_IP 9080 2>/dev/null"

    run_test "Synology DSM HTTP accessible" \
        "nc -z -w5 $SYNOLOGY_IP 5000 2>/dev/null"

    run_test "Synology DSM HTTPS accessible" \
        "nc -z -w5 $SYNOLOGY_IP 5001 2>/dev/null"
    
    # Test internet access via VPN
    run_test "Internet access via VPN" \
        "ping -c 3 -W 5 ******* >/dev/null 2>&1"
}

# Function to test DNS configuration
test_dns_configuration() {
    echo ""
    echo "=== Testing DNS Configuration ==="
    
    for domain in "${DOMAINS[@]}"; do
        local resolved_ip=$(nslookup "$domain" 2>/dev/null | grep -A1 "Name:" | tail -1 | awk '{print $2}')

        if [[ "$resolved_ip" == "*************" ]]; then
            run_test "DNS resolves $domain to Synology NAS IP" "true"
        elif [[ "$resolved_ip" == "192.168."* ]]; then
            run_test "DNS resolves $domain to internal IP" "true"
        else
            run_test "DNS resolves $domain to internal IP" "false"
        fi
    done
}

# Function to test SSL certificates
test_ssl_certificates() {
    echo ""
    echo "=== Testing SSL Certificates ==="
    
    for domain in "${DOMAINS[@]}"; do
        run_test "SSL certificate valid for $domain" \
            "echo | openssl s_client -connect $domain:443 -servername $domain 2>/dev/null | openssl x509 -noout -dates >/dev/null 2>&1"
    done
}

# Function to generate test report
generate_report() {
    echo ""
    echo "=== Test Report ==="
    echo "Total tests run: $total_tests"
    echo "Tests passed: $passed_tests"
    echo "Tests failed: $failed_tests"
    echo "Success rate: $(( passed_tests * 100 / total_tests ))%"
    echo ""
    
    if [ $failed_tests -eq 0 ]; then
        echo "🎉 ALL TESTS PASSED! Your VPN-only setup is working correctly."
        echo ""
        echo "Security Status: ✅ SECURE"
        echo "- Services are not publicly accessible"
        echo "- VPN access is working properly"
        echo "- DNS resolution is configured correctly"
        echo "- SSL certificates are valid"
    else
        echo "⚠️  SOME TESTS FAILED. Please review the configuration."
        echo ""
        echo "Security Status: ❌ NEEDS ATTENTION"
        echo "- Review failed tests above"
        echo "- Check firewall rules and port forwarding"
        echo "- Verify VPN configuration"
        echo "- Test DNS settings"
    fi
    
    echo ""
    echo "Recommendations:"
    echo "1. Run this test from multiple external networks"
    echo "2. Test with different VPN clients"
    echo "3. Monitor logs for any suspicious activity"
    echo "4. Regularly update and patch all systems"
}

# Main execution
echo "Starting comprehensive test suite..."
echo "Configuration:"
echo "- External IP: $EXTERNAL_IP"
echo "- Domains: ${DOMAINS[*]}"
echo "- Synology NAS: $SYNOLOGY_IP"
echo "- AdGuard Home: $ADGUARD_IP"
echo "- Traefik: $TRAEFIK_IP"
echo "- Traefik Ports: 8080 (HTTP), 8443 (HTTPS), 9080 (Dashboard)"
echo ""

# Check VPN status
vpn_connected=$(check_vpn_status)
vpn_status=$?

if [ $vpn_status -eq 0 ]; then
    echo "Testing VPN functionality..."
    test_vpn_access
    test_dns_configuration
    test_ssl_certificates
else
    echo "Testing external access blocking..."
    test_external_blocking
fi

# Generate final report
generate_report

# Exit with appropriate code
if [ $failed_tests -eq 0 ]; then
    exit 0
else
    exit 1
fi
