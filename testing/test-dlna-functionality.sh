#!/bin/bash

# DLNA Functionality Testing Script
# Tests DLNA discovery, streaming, and multicast functionality
# for the optimized media network configuration

echo "=== DLNA Functionality Testing ==="
echo "Testing DLNA discovery, streaming, and multicast functionality"
echo "Date: $(date)"
echo ""

# Make script executable
chmod +x "$0" 2>/dev/null || true

# Configuration
MEDIA_NETWORK="***********/24"
YAMAHA_SPEAKERS=(
    "************:livingroom"
    "************:kitchen"
    "************:bureau"
    "************:bedroom"
)
JELLYFIN_SERVER="*************:8096"
SYNOLOGY_MEDIA="*************:5000"
DLNA_MULTICAST="***************:1900"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC}: $test_name"
        [ -n "$details" ] && echo "  Details: $details"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC}: $test_name"
        [ -n "$details" ] && echo "  Error: $details"
        ((TESTS_FAILED++))
    fi
    echo ""
}

# Function to test network connectivity
test_connectivity() {
    echo -e "${BLUE}=== Network Connectivity Tests ===${NC}"
    
    # Test media network gateway
    if ping -c 3 -W 2 *********** >/dev/null 2>&1; then
        print_result "Media Network Gateway" "PASS" "*********** is reachable"
    else
        print_result "Media Network Gateway" "FAIL" "*********** is not reachable"
    fi
    
    # Test Yamaha speakers connectivity
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if ping -c 3 -W 2 "$ip" >/dev/null 2>&1; then
            print_result "Yamaha Speaker ($name)" "PASS" "$ip is reachable"
        else
            print_result "Yamaha Speaker ($name)" "FAIL" "$ip is not reachable"
        fi
    done
    
    # Test Jellyfin server
    if ping -c 3 -W 2 ************* >/dev/null 2>&1; then
        print_result "Jellyfin Server" "PASS" "************* is reachable"
    else
        print_result "Jellyfin Server" "FAIL" "************* is not reachable"
    fi
}

# Function to test DLNA multicast discovery
test_dlna_discovery() {
    echo -e "${BLUE}=== DLNA Discovery Tests ===${NC}"
    
    # Test DLNA multicast address reachability
    if ping -c 3 -W 2 *************** >/dev/null 2>&1; then
        print_result "DLNA Multicast Address" "PASS" "*************** is reachable"
    else
        print_result "DLNA Multicast Address" "FAIL" "*************** is not reachable"
    fi
    
    # Test UPnP discovery using upnpc (if available)
    if command -v upnpc >/dev/null 2>&1; then
        echo "Testing UPnP device discovery..."
        upnp_output=$(timeout 10 upnpc -l 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$upnp_output" ]; then
            device_count=$(echo "$upnp_output" | grep -c "desc:")
            print_result "UPnP Device Discovery" "PASS" "Found $device_count UPnP devices"
            echo "  Devices found:"
            echo "$upnp_output" | grep "desc:" | head -5
        else
            print_result "UPnP Device Discovery" "FAIL" "No UPnP devices found or timeout"
        fi
    else
        print_result "UPnP Discovery Tool" "SKIP" "upnpc not available (install miniupnpc)"
    fi
    
    # Test SSDP discovery using netcat (if available)
    if command -v nc >/dev/null 2>&1; then
        echo "Testing SSDP discovery..."
        ssdp_request="M-SEARCH * HTTP/1.1\r\nHOST: ***************:1900\r\nMAN: \"ssdp:discover\"\r\nST: upnp:rootdevice\r\nMX: 3\r\n\r\n"
        ssdp_response=$(echo -e "$ssdp_request" | timeout 5 nc -u *************** 1900 2>/dev/null)
        if [ -n "$ssdp_response" ]; then
            print_result "SSDP Discovery" "PASS" "Received SSDP responses"
        else
            print_result "SSDP Discovery" "FAIL" "No SSDP responses received"
        fi
    else
        print_result "SSDP Discovery Tool" "SKIP" "netcat not available"
    fi
}

# Function to test media server accessibility
test_media_servers() {
    echo -e "${BLUE}=== Media Server Tests ===${NC}"
    
    # Test Jellyfin HTTP access
    if curl -s --connect-timeout 5 "http://$JELLYFIN_SERVER" >/dev/null; then
        print_result "Jellyfin HTTP Access" "PASS" "http://$JELLYFIN_SERVER is accessible"
    else
        print_result "Jellyfin HTTP Access" "FAIL" "http://$JELLYFIN_SERVER is not accessible"
    fi
    
    # Test Synology Media Server
    if curl -s --connect-timeout 5 "http://$SYNOLOGY_MEDIA" >/dev/null; then
        print_result "Synology Media Server" "PASS" "http://$SYNOLOGY_MEDIA is accessible"
    else
        print_result "Synology Media Server" "FAIL" "http://$SYNOLOGY_MEDIA is not accessible"
    fi
    
    # Test DLNA content directory service (if available)
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if curl -s --connect-timeout 5 "http://$ip" >/dev/null; then
            print_result "Yamaha Speaker Web Interface ($name)" "PASS" "http://$ip is accessible"
        else
            print_result "Yamaha Speaker Web Interface ($name)" "FAIL" "http://$ip is not accessible"
        fi
    done
}

# Function to test multicast traffic
test_multicast_traffic() {
    echo -e "${BLUE}=== Multicast Traffic Tests ===${NC}"
    
    # Test multicast group membership
    if ip maddr show | grep -q "***************"; then
        print_result "Multicast Group Membership" "PASS" "Device is member of DLNA multicast group"
    else
        print_result "Multicast Group Membership" "FAIL" "Device is not member of DLNA multicast group"
    fi
    
    # Test IGMP functionality
    if [ -f /proc/net/igmp ]; then
        igmp_groups=$(cat /proc/net/igmp | grep -c "***************")
        if [ "$igmp_groups" -gt 0 ]; then
            print_result "IGMP Group Registration" "PASS" "DLNA multicast group is registered"
        else
            print_result "IGMP Group Registration" "FAIL" "DLNA multicast group is not registered"
        fi
    else
        print_result "IGMP Information" "SKIP" "/proc/net/igmp not available"
    fi
    
    # Test multicast routing
    if command -v netstat >/dev/null 2>&1; then
        mcast_routes=$(netstat -rn | grep -c "*********")
        if [ "$mcast_routes" -gt 0 ]; then
            print_result "Multicast Routing" "PASS" "Multicast routes are configured"
        else
            print_result "Multicast Routing" "FAIL" "No multicast routes found"
        fi
    else
        print_result "Multicast Routing Check" "SKIP" "netstat not available"
    fi
}

# Function to test DNS resolution for media devices
test_dns_resolution() {
    echo -e "${BLUE}=== DNS Resolution Tests ===${NC}"
    
    # Test DNS resolution for speaker hostnames
    speaker_domains=(
        "livingroom.mdewaele.freeddns.org:************"
        "kitchen.mdewaele.freeddns.org:************"
        "bureau.mdewaele.freeddns.org:************"
        "bedroom.mdewaele.freeddns.org:************"
    )
    
    for domain_ip in "${speaker_domains[@]}"; do
        IFS=':' read -r domain expected_ip <<< "$domain_ip"
        resolved_ip=$(nslookup "$domain" 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')
        if [ "$resolved_ip" = "$expected_ip" ]; then
            print_result "DNS Resolution ($domain)" "PASS" "Resolves to $resolved_ip"
        else
            print_result "DNS Resolution ($domain)" "FAIL" "Expected $expected_ip, got $resolved_ip"
        fi
    done
    
    # Test reverse DNS resolution
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        reverse_lookup=$(nslookup "$ip" 2>/dev/null | grep "name =" | awk '{print $4}')
        if [ -n "$reverse_lookup" ]; then
            print_result "Reverse DNS ($ip)" "PASS" "Resolves to $reverse_lookup"
        else
            print_result "Reverse DNS ($ip)" "FAIL" "No reverse DNS entry found"
        fi
    done
}

# Function to test streaming performance
test_streaming_performance() {
    echo -e "${BLUE}=== Streaming Performance Tests ===${NC}"
    
    # Test bandwidth to media network
    if command -v iperf3 >/dev/null 2>&1; then
        echo "Note: iperf3 performance testing requires iperf3 server running on target devices"
        print_result "Bandwidth Testing Tool" "AVAILABLE" "iperf3 is available for performance testing"
    else
        print_result "Bandwidth Testing Tool" "SKIP" "iperf3 not available (install iperf3)"
    fi
    
    # Test latency to speakers
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        latency=$(ping -c 5 -W 2 "$ip" 2>/dev/null | grep "avg" | awk -F'/' '{print $5}')
        if [ -n "$latency" ]; then
            latency_ms=$(echo "$latency" | cut -d'.' -f1)
            if [ "$latency_ms" -lt 10 ]; then
                print_result "Latency to $name Speaker" "PASS" "${latency}ms (excellent)"
            elif [ "$latency_ms" -lt 50 ]; then
                print_result "Latency to $name Speaker" "PASS" "${latency}ms (good)"
            else
                print_result "Latency to $name Speaker" "WARN" "${latency}ms (high latency)"
            fi
        else
            print_result "Latency to $name Speaker" "FAIL" "Unable to measure latency"
        fi
    done
}

# Function to test firewall rules
test_firewall_rules() {
    echo -e "${BLUE}=== Firewall Rules Tests ===${NC}"
    
    # Test if DLNA multicast port is open
    if command -v nmap >/dev/null 2>&1; then
        echo "Testing DLNA port accessibility..."
        for speaker in "${YAMAHA_SPEAKERS[@]}"; do
            IFS=':' read -r ip name <<< "$speaker"
            port_scan=$(timeout 10 nmap -p 1900 -sU "$ip" 2>/dev/null | grep "1900/udp")
            if echo "$port_scan" | grep -q "open"; then
                print_result "DLNA Port ($name)" "PASS" "Port 1900/UDP is open"
            else
                print_result "DLNA Port ($name)" "FAIL" "Port 1900/UDP is not accessible"
            fi
        done
    else
        print_result "Port Scanning Tool" "SKIP" "nmap not available"
    fi
    
    # Test HTTP ports on speakers
    for speaker in "${YAMAHA_SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if timeout 5 bash -c "</dev/tcp/$ip/80" 2>/dev/null; then
            print_result "HTTP Port ($name)" "PASS" "Port 80 is accessible"
        else
            print_result "HTTP Port ($name)" "FAIL" "Port 80 is not accessible"
        fi
    done
}

# Main execution
main() {
    echo "Starting DLNA functionality tests..."
    echo "This script tests the optimized media network configuration"
    echo ""
    
    # Check if running with appropriate permissions
    if [ "$EUID" -eq 0 ]; then
        echo -e "${YELLOW}Warning: Running as root. Some tests may behave differently.${NC}"
        echo ""
    fi
    
    # Run all test suites
    test_connectivity
    test_dns_resolution
    test_dlna_discovery
    test_media_servers
    test_multicast_traffic
    test_streaming_performance
    test_firewall_rules
    
    # Print summary
    echo -e "${BLUE}=== Test Summary ===${NC}"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}All tests passed! DLNA functionality is working correctly.${NC}"
        exit 0
    else
        echo -e "${RED}Some tests failed. Please review the configuration and network setup.${NC}"
        echo ""
        echo "Common issues and solutions:"
        echo "1. Speakers not reachable: Check VLAN configuration and DHCP reservations"
        echo "2. DLNA discovery fails: Verify IGMP proxy and mDNS repeater configuration"
        echo "3. Multicast issues: Check firewall rules for multicast traffic"
        echo "4. DNS resolution fails: Verify AdGuard Home DNS rewrites"
        echo "5. High latency: Check QoS configuration and network congestion"
        exit 1
    fi
}

# Run the main function
main "$@"
