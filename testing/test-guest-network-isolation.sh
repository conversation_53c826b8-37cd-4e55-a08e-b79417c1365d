#!/bin/bash
# Guest Network Isolation Test Script
# Run this script from a device connected to the guest network (**********/24)
# This script verifies that guest network is properly isolated from internal networks

echo "=== Guest Network Isolation Test ==="
echo "Testing that guest network (**********/24) is completely isolated"
echo "Run this from a device connected to the GUEST NETWORK"
echo ""
echo "Network Configuration:"
echo "- Guest Network: **********/24 (should be isolated)"
echo "- Wired LAN: ***********/24 (should be blocked)"
echo "- WiFi LAN: ***********/24 (should be blocked)"
echo "- VPN Network: ***********/24 (should be blocked)"
echo "- Synology NAS: ************* (should be blocked)"
echo ""

# Test targets that should be BLOCKED - COMPLETE SERVICE INVENTORY
BLOCKED_TARGETS=(
    # EdgeRouter Management
    "***********:22"      # EdgeRouter SSH
    "***********:80"      # EdgeRouter Web UI
    "***********:443"     # EdgeRouter Web UI HTTPS
    "***********:51820"   # WireGuard VPN Server

    # Synology NAS Services
    "*************:80"    # Synology NAS HTTP
    "*************:443"   # Synology NAS HTTPS
    "*************:5000"  # Synology DSM HTTP
    "*************:5001"  # Synology DSM HTTPS
    "*************:53"    # Internal DNS

    # Traefik Services
    "*************:8080"  # Traefik HTTP
    "*************:8443"  # Traefik HTTPS
    "*************:9080"  # Traefik Dashboard

    # Core Infrastructure Services
    "*************:3000"  # AdGuard Home
    "*************:9000"  # Portainer

    # Home Automation & IoT Services
    "*************:8123"  # Home Assistant
    "*************:8096"  # Jellyfin Media Server
    "*************:8880"  # Hotspot Manager (UniFi)

    # Media & Entertainment Services
    "*************:8081"  # Custom Media App
    "*************:8082"  # Media API (PHP)

    # Network Services
    "***********:80"      # WiFi Gateway (if exists)
    "***********:22"      # VPN Gateway

    # Static Device Access (Yamaha speakers - update IPs as needed)
    # "192.168.1.XXX:80"   # Living Room Speaker
    # "192.168.1.XXX:80"   # Kitchen Speaker
    # "192.168.1.XXX:80"   # Bureau Speaker
    # "192.168.1.XXX:80"   # Bedroom Speaker
)

# Test targets that should be ALLOWED
ALLOWED_TARGETS=(
    "*******:53"          # Google DNS
    "*******:53"          # Cloudflare DNS
    "*******"             # Google DNS (ping)
    "*******"             # Cloudflare DNS (ping)
)

# Test counters
total_tests=0
passed_tests=0
failed_tests=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"  # "pass" or "fail"
    
    echo "Testing: $test_name"
    ((total_tests++))
    
    if eval "$test_command" >/dev/null 2>&1; then
        local actual_result="pass"
    else
        local actual_result="fail"
    fi
    
    if [ "$actual_result" = "$expected_result" ]; then
        if [ "$expected_result" = "fail" ]; then
            echo "✅ SECURE: $test_name (correctly blocked)"
        else
            echo "✅ WORKING: $test_name (correctly allowed)"
        fi
        ((passed_tests++))
    else
        if [ "$expected_result" = "fail" ]; then
            echo "❌ SECURITY ISSUE: $test_name (should be blocked but is accessible)"
        else
            echo "❌ CONNECTIVITY ISSUE: $test_name (should be allowed but is blocked)"
        fi
        ((failed_tests++))
    fi
}

# Function to check if running on guest network
check_guest_network() {
    echo "=== Checking Network Configuration ==="
    
    # Check IP address
    local ip_addr=$(ip route get ******* | grep -oP 'src \K\S+' 2>/dev/null)
    
    if [[ "$ip_addr" == "172.16.1."* ]]; then
        echo "✅ Running on guest network: $ip_addr"
        return 0
    else
        echo "❌ WARNING: Not running on guest network (IP: $ip_addr)"
        echo "This test should be run from a device connected to the guest network (**********/24)"
        echo "Continue anyway? (y/N)"
        read -r response
        if [[ "$response" != "y" && "$response" != "Y" ]]; then
            exit 1
        fi
        return 1
    fi
}

# Function to test network connectivity
test_network_connectivity() {
    local target="$1"
    local timeout=5
    
    if [[ "$target" == *":"* ]]; then
        # Test specific port
        local host=$(echo "$target" | cut -d':' -f1)
        local port=$(echo "$target" | cut -d':' -f2)
        
        if command -v nc >/dev/null 2>&1; then
            nc -z -w"$timeout" "$host" "$port" 2>/dev/null
        elif command -v telnet >/dev/null 2>&1; then
            timeout "$timeout" telnet "$host" "$port" 2>/dev/null | grep -q "Connected"
        else
            # Fallback to ping for host-only tests
            ping -c 1 -W "$timeout" "$host" >/dev/null 2>&1
        fi
    else
        # Test ping connectivity
        ping -c 1 -W "$timeout" "$target" >/dev/null 2>&1
    fi
}

# Function to test HTTP/HTTPS access
test_web_access() {
    local url="$1"
    local timeout=10
    
    if command -v curl >/dev/null 2>&1; then
        curl -s --connect-timeout "$timeout" --max-time "$timeout" "$url" >/dev/null 2>&1
    elif command -v wget >/dev/null 2>&1; then
        wget -q --timeout="$timeout" --tries=1 -O /dev/null "$url" >/dev/null 2>&1
    else
        return 1
    fi
}

# Main testing
echo "Starting guest network isolation tests..."
echo ""

# Check network configuration
guest_network_confirmed=$(check_guest_network)

echo ""
echo "=== Testing Internal Network Blocking ==="

# Test blocked targets
for target in "${BLOCKED_TARGETS[@]}"; do
    run_test "Block access to $target" "test_network_connectivity $target" "fail"
done

echo ""
echo "=== Testing Internet Access ==="

# Test allowed targets
for target in "${ALLOWED_TARGETS[@]}"; do
    run_test "Allow access to $target" "test_network_connectivity $target" "pass"
done

# Test web access to internet
run_test "HTTP access to internet" "test_web_access http://www.google.com" "pass"
run_test "HTTPS access to internet" "test_web_access https://www.google.com" "pass"

echo ""
echo "=== Testing DNS Resolution ==="

# Test DNS resolution (should work with public DNS, fail with internal)
run_test "DNS resolution (public)" "nslookup google.com ******* >/dev/null 2>&1" "pass"
run_test "Internal DNS blocked" "! nslookup google.com ************* >/dev/null 2>&1" "pass"

echo ""
echo "=== Testing Subdomain Access (Should be Blocked) ==="

# Test that ALL internal subdomains are not accessible
INTERNAL_DOMAINS=(
    # Core Infrastructure Services
    "home.mdewaele.freeddns.org"
    "traefik.mdewaele.freeddns.org"
    "lb.mdewaele.freeddns.org"
    "adguard.mdewaele.freeddns.org"
    "router.mdewaele.freeddns.org"
    "nas.mdewaele.freeddns.org"
    "portainer.mdewaele.freeddns.org"

    # Home Automation & IoT Services
    "jellyfin.mdewaele.freeddns.org"
    "zigbee.mdewaele.freeddns.org"
    "unifi.mdewaele.freeddns.org"
    "hotspot.mdewaele.freeddns.org"

    # Media & Entertainment Services
    "media.mdewaele.freeddns.org"
    "api.media.mdewaele.freeddns.org"

    # Static Device Mappings (Yamaha speakers)
    "livingroom.mdewaele.freeddns.org"
    "kitchen.mdewaele.freeddns.org"
    "bureau.mdewaele.freeddns.org"
    "bedroom.mdewaele.freeddns.org"
)

for domain in "${INTERNAL_DOMAINS[@]}"; do
    run_test "Block HTTP access to $domain" "! test_web_access http://$domain" "pass"
    run_test "Block HTTPS access to $domain" "! test_web_access https://$domain" "pass"
done

echo ""
echo "=== Test Results Summary ==="

echo "Total tests run: $total_tests"
echo "Tests passed: $passed_tests"
echo "Tests failed: $failed_tests"

if [ $failed_tests -eq 0 ]; then
    echo ""
    echo "🎉 ALL TESTS PASSED! Guest network is properly isolated."
    echo ""
    echo "Security Status: ✅ SECURE"
    echo "- Guest network cannot access internal networks"
    echo "- Guest network cannot access internal services"
    echo "- Guest network has internet access only"
    echo "- Internal DNS is blocked"
    echo "- VPN server is not accessible from guest network"
else
    echo ""
    echo "⚠️  SOME TESTS FAILED. Guest network isolation may be compromised."
    echo ""
    echo "Security Status: ❌ NEEDS ATTENTION"
    echo "- Review failed tests above"
    echo "- Check EdgeRouter firewall rules"
    echo "- Verify VLAN configuration"
    echo "- Ensure guest network isolation rules are applied"
fi

echo ""
echo "Success rate: $(( passed_tests * 100 / total_tests ))%"

echo ""
echo "Recommendations:"
echo "1. Run this test from multiple guest network devices"
echo "2. Test at different times to ensure consistent blocking"
echo "3. Monitor EdgeRouter logs for blocked connection attempts"
echo "4. Verify guest network users cannot access VPN configuration"

# Exit with appropriate code
if [ $failed_tests -eq 0 ]; then
    exit 0
else
    exit 1
fi
