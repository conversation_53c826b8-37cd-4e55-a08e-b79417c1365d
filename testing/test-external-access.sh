#!/bin/bash
# Test script to verify services are NOT accessible from external networks
# Run this script from a device OUTSIDE your network (mobile data, different ISP, VPS, etc.)
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: ***********/24

echo "=== External Access Security Test ==="
echo "Testing that services are NOT accessible from external networks"
echo "Run this from OUTSIDE your home network (mobile data, VPS, etc.)"
echo ""
echo "Network Configuration:"
echo "- Synology NAS: ************* (hosting ALL containerized services)"
echo "- Testing ALL services and static devices for external blocking"
echo "- Traefik Ports: 8080 (HTTP), 8443 (HTTPS) - should be blocked"
echo "- Standard Ports: 80, 443 - should be blocked"
echo "- Container Ports: 3000, 8081, 8082, 8096, 8123, 8880, 9000, 9080 - should be blocked"
echo ""

# Configuration - COMPLETE SERVICE INVENTORY
DOMAINS=(
    # Core Infrastructure Services
    "home.mdewaele.freeddns.org"
    "traefik.mdewaele.freeddns.org"
    "lb.mdewaele.freeddns.org"
    "adguard.mdewaele.freeddns.org"
    "router.mdewaele.freeddns.org"
    "nas.mdewaele.freeddns.org"
    "portainer.mdewaele.freeddns.org"

    # Home Automation & IoT Services
    "jellyfin.mdewaele.freeddns.org"
    "zigbee.mdewaele.freeddns.org"
    "unifi.mdewaele.freeddns.org"
    "hotspot.mdewaele.freeddns.org"

    # Media & Entertainment Services
    "media.mdewaele.freeddns.org"
    "api.media.mdewaele.freeddns.org"

    # Static Device Mappings (Yamaha speakers)
    "livingroom.mdewaele.freeddns.org"
    "kitchen.mdewaele.freeddns.org"
    "bureau.mdewaele.freeddns.org"
    "bedroom.mdewaele.freeddns.org"
)

# All service ports that should be blocked externally
PORTS=(80 443 3000 5000 5001 8080 8081 8082 8096 8123 8443 8880 9000 9080)
EXTERNAL_IP="YOUR_EXTERNAL_IP"  # Replace with your actual external IP

echo "Testing domains: ${DOMAINS[@]}"
echo "Testing ports: ${PORTS[@]}"
echo "External IP: $EXTERNAL_IP"
echo ""

# Function to test HTTP/HTTPS access
test_web_access() {
    local domain=$1
    local protocol=$2
    local port=$3
    
    echo "Testing $protocol://$domain:$port"
    
    # Test with curl (timeout after 10 seconds)
    if curl -s --connect-timeout 10 --max-time 15 "$protocol://$domain:$port" > /dev/null 2>&1; then
        echo "❌ SECURITY ISSUE: $protocol://$domain:$port is accessible from external network!"
        return 1
    else
        echo "✅ SECURE: $protocol://$domain:$port is not accessible from external network"
        return 0
    fi
}

# Function to test port connectivity
test_port_access() {
    local ip=$1
    local port=$2
    
    echo "Testing port $port on $ip"
    
    # Test with netcat or telnet
    if command -v nc >/dev/null 2>&1; then
        if nc -z -w5 "$ip" "$port" 2>/dev/null; then
            echo "❌ SECURITY ISSUE: Port $port is open on $ip!"
            return 1
        else
            echo "✅ SECURE: Port $port is closed on $ip"
            return 0
        fi
    elif command -v telnet >/dev/null 2>&1; then
        if timeout 5 telnet "$ip" "$port" 2>/dev/null | grep -q "Connected"; then
            echo "❌ SECURITY ISSUE: Port $port is open on $ip!"
            return 1
        else
            echo "✅ SECURE: Port $port is closed on $ip"
            return 0
        fi
    else
        echo "⚠️  WARNING: Neither nc nor telnet available for port testing"
        return 0
    fi
}

# Function to test DNS resolution
test_dns_resolution() {
    local domain=$1
    
    echo "Testing DNS resolution for $domain"
    
    if nslookup "$domain" >/dev/null 2>&1; then
        local resolved_ip=$(nslookup "$domain" | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        echo "✅ DNS resolves $domain to $resolved_ip"
        
        # Check if it resolves to external IP (expected) or internal IP (potential issue)
        if [[ "$resolved_ip" == "192.168."* ]] || [[ "$resolved_ip" == "10."* ]] || [[ "$resolved_ip" == "172."* ]]; then
            echo "⚠️  WARNING: $domain resolves to internal IP $resolved_ip from external network"
        fi
    else
        echo "❌ DNS resolution failed for $domain"
    fi
}

# Main testing
echo "=== Starting Security Tests ==="
echo ""

security_issues=0

# Test 1: Web access to domains
echo "1. Testing web access to domains..."
for domain in "${DOMAINS[@]}"; do
    echo ""
    test_dns_resolution "$domain"
    
    # Test HTTP
    if ! test_web_access "$domain" "http" "80"; then
        ((security_issues++))
    fi
    
    # Test HTTPS
    if ! test_web_access "$domain" "https" "443"; then
        ((security_issues++))
    fi
    
    # Test Traefik alternative ports
    if ! test_web_access "$domain" "http" "8080"; then
        ((security_issues++))
    fi

    if ! test_web_access "$domain" "https" "8443"; then
        ((security_issues++))
    fi
done

echo ""
echo "2. Testing direct IP access..."

# Test 2: Direct IP port access
for port in "${PORTS[@]}"; do
    if ! test_port_access "$EXTERNAL_IP" "$port"; then
        ((security_issues++))
    fi
done

echo ""
echo "3. Testing WireGuard port (should be accessible)..."

# Test 3: WireGuard port should be accessible
if test_port_access "$EXTERNAL_IP" "51820"; then
    echo "✅ EXPECTED: WireGuard port 51820 is accessible"
else
    echo "❌ ISSUE: WireGuard port 51820 is not accessible - VPN won't work!"
    ((security_issues++))
fi

echo ""
echo "=== Test Results ==="

if [ $security_issues -eq 0 ]; then
    echo "✅ SECURITY TEST PASSED: No services are publicly accessible"
    echo "Your setup is secure - services are only accessible via VPN"
else
    echo "❌ SECURITY TEST FAILED: $security_issues security issues found"
    echo "Services are still publicly accessible - review your configuration"
fi

echo ""
echo "Next steps:"
echo "1. If test passed: Proceed to test VPN access"
echo "2. If test failed: Review firewall rules and port forwarding configuration"
echo "3. Test from multiple external networks to be sure"

exit $security_issues
