#!/bin/bash

# DS923+ Dual NIC LAG - Comprehensive VPN-Only Access Test Suite
# This script validates all aspects of the optimized VPN-only access setup
# including DS923+ Link Aggregation and complete Ubiquiti infrastructure

echo "=== DS923+ Dual NIC LAG - VPN-Only Access Test Suite ==="
echo "Testing Link Aggregation, network segmentation, security policies, and service accessibility"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if eval "$test_command"; then
        if [ "$expected_result" = "success" ]; then
            echo -e "${GREEN}✓ PASS: $test_name${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo -e "${RED}✗ FAIL: $test_name (expected failure but got success)${NC}"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            echo -e "${GREEN}✓ PASS: $test_name (correctly blocked)${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo -e "${RED}✗ FAIL: $test_name${NC}"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    fi
    echo ""
}

# Network configuration
NAS_IP="*************"
EDGEROUTER_IP="***********"
USW_FLEX_IP="***********"
UNIFI_AC_PRO_IP="***********"
UNIFI_U6_LITE_IP="***********"
SWITCH_24P_IP="***********"
SWITCH_4P_POE_IP="***********"

# Service ports
TRAEFIK_PORT="8080"
ADGUARD_PORT="3000"
HOME_ASSISTANT_PORT="8123"
JELLYFIN_PORT="8096"
PORTAINER_PORT="9000"
SYNOLOGY_DSM_PORT="5000"
UNIFI_CONTROLLER_PORT="8443"

echo "=== Phase 1: DS923+ Link Aggregation Performance Tests ==="
echo ""

# Test 1: DS923+ LAG Interface Status
run_test "DS923+ LAG Interface Status" \
    "ping -c 3 $NAS_IP > /dev/null 2>&1" \
    "success"

# Test 2: LAG Bandwidth Test (requires iperf3)
if command -v iperf3 &> /dev/null; then
    echo -e "${BLUE}Testing: DS923+ LAG Bandwidth Performance${NC}"
    echo "Starting iperf3 server test (requires iperf3 server running on NAS)"
    echo "Expected: ~1.9 Gbps aggregate bandwidth"
    iperf3 -c $NAS_IP -t 10 -P 4 2>/dev/null || echo "Note: iperf3 server not running on NAS"
    echo ""
else
    echo -e "${YELLOW}Warning: iperf3 not installed - skipping bandwidth test${NC}"
    echo ""
fi

echo "=== Phase 2: Infrastructure Connectivity Tests ==="
echo ""

# Test 3-8: Infrastructure Device Connectivity
run_test "EdgeRouter 4 Connectivity" \
    "ping -c 3 $EDGEROUTER_IP > /dev/null 2>&1" \
    "success"

run_test "USW Flex 2.5G Connectivity" \
    "ping -c 3 $USW_FLEX_IP > /dev/null 2>&1" \
    "success"

run_test "UniFi AC Pro Connectivity" \
    "ping -c 3 $UNIFI_AC_PRO_IP > /dev/null 2>&1" \
    "success"

run_test "UniFi U6 Lite Connectivity" \
    "ping -c 3 $UNIFI_U6_LITE_IP > /dev/null 2>&1" \
    "success"

run_test "24-Port Switch Connectivity" \
    "ping -c 3 $SWITCH_24P_IP > /dev/null 2>&1" \
    "success"

run_test "4-Port PoE Switch Connectivity" \
    "ping -c 3 $SWITCH_4P_POE_IP > /dev/null 2>&1" \
    "success"

echo "=== Phase 3: Core Service Accessibility Tests ==="
echo ""

# Test 9-15: Core Services on DS923+
run_test "Traefik Dashboard Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$TRAEFIK_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "AdGuard Home Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$ADGUARD_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "Home Assistant Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$HOME_ASSISTANT_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "Jellyfin Media Server Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$JELLYFIN_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "Portainer Container Manager Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$PORTAINER_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "Synology DSM Access" \
    "curl -s -o /dev/null -w '%{http_code}' http://$NAS_IP:$SYNOLOGY_DSM_PORT | grep -q '200\|401\|403'" \
    "success"

run_test "UniFi Controller Access" \
    "curl -s -k -o /dev/null -w '%{http_code}' https://$NAS_IP:$UNIFI_CONTROLLER_PORT | grep -q '200\|401\|403'" \
    "success"

echo "=== Phase 4: Network Segmentation Tests ==="
echo ""

# Test 16-19: VLAN Connectivity
echo -e "${BLUE}Testing VLAN Gateway Connectivity${NC}"
run_test "Core Infrastructure VLAN (***********)" \
    "ping -c 3 *********** > /dev/null 2>&1" \
    "success"

run_test "Guest Network VLAN (**********)" \
    "ping -c 3 ********** > /dev/null 2>&1" \
    "success"

run_test "Media Network VLAN (***********)" \
    "ping -c 3 *********** > /dev/null 2>&1" \
    "success"

run_test "Primary WiFi VLAN (************)" \
    "ping -c 3 ************ > /dev/null 2>&1" \
    "success"

echo "=== Phase 5: DLNA Media Network Tests ==="
echo ""

# Test 20-23: Media Network Devices (Yamaha Speakers)
echo -e "${BLUE}Testing Yamaha MusicCast Speaker Connectivity${NC}"
run_test "Living Room Speaker (***********0)" \
    "ping -c 3 ***********0 > /dev/null 2>&1" \
    "success"

run_test "Kitchen Speaker (***********1)" \
    "ping -c 3 ***********1 > /dev/null 2>&1" \
    "success"

run_test "Bureau Speaker (***********2)" \
    "ping -c 3 ***********2 > /dev/null 2>&1" \
    "success"

run_test "Bedroom Speaker (************)" \
    "ping -c 3 ************ > /dev/null 2>&1" \
    "success"

echo "=== Phase 6: DNS Resolution Tests ==="
echo ""

# Test 24-27: DNS Resolution
run_test "Internal DNS Resolution (home.mdewaele.freeddns.org)" \
    "nslookup home.mdewaele.freeddns.org | grep -q '$NAS_IP'" \
    "success"

run_test "Internal DNS Resolution (traefik.mdewaele.freeddns.org)" \
    "nslookup traefik.mdewaele.freeddns.org | grep -q '$NAS_IP'" \
    "success"

run_test "Internal DNS Resolution (jellyfin.mdewaele.freeddns.org)" \
    "nslookup jellyfin.mdewaele.freeddns.org | grep -q '$NAS_IP'" \
    "success"

run_test "External DNS Resolution (google.com)" \
    "nslookup google.com > /dev/null 2>&1" \
    "success"

echo "=== Phase 7: Security Validation Tests ==="
echo ""

# Test 28-30: External Access Blocking
echo -e "${BLUE}Testing External Access Blocking (should fail)${NC}"
echo "Note: These tests should FAIL to confirm security is working"

# These tests should fail if security is properly configured
run_test "External HTTP Access Blocking" \
    "timeout 5 curl -s http://$(curl -s ifconfig.me):$TRAEFIK_PORT > /dev/null 2>&1" \
    "fail"

run_test "External HTTPS Access Blocking" \
    "timeout 5 curl -s https://$(curl -s ifconfig.me):8443 > /dev/null 2>&1" \
    "fail"

run_test "External SSH Access Blocking" \
    "timeout 5 nc -z $(curl -s ifconfig.me) 22 > /dev/null 2>&1" \
    "fail"

echo "=== Test Results Summary ==="
echo ""
echo -e "Total Tests: $TESTS_TOTAL"
echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Failed: $TESTS_FAILED${NC}"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! DS923+ Dual NIC LAG VPN-only access configuration is working correctly.${NC}"
    exit 0
else
    echo -e "${RED}⚠️  Some tests failed. Please review the configuration and network setup.${NC}"
    exit 1
fi
