#!/bin/bash
# Test script to verify services ARE accessible when connected via VPN
# Run this script from a device connected to the WireGuard VPN
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: ***********/24

echo "=== VPN Access Functionality Test ==="
echo "Testing that services ARE accessible when connected via VPN"
echo "Make sure you're connected to WireGuard VPN before running this script"
echo ""
echo "Network Configuration:"
echo "- Synology NAS: ************* (hosting ALL containerized services)"
echo "- Testing ALL services and static devices for VPN accessibility"
echo "- Traefik Ports: 8080 (HTTP), 8443 (HTTPS)"
echo "- Container Services: Home Assistant, Jellyfin, UniFi, Zigbee, etc."
echo "- Static Devices: Yamaha MusicCast speakers"
echo ""

# Configuration - COMPLETE SERVICE INVENTORY
DOMAINS=(
    # Core Infrastructure Services
    "home.mdewaele.freeddns.org"
    "traefik.mdewaele.freeddns.org"
    "lb.mdewaele.freeddns.org"
    "adguard.mdewaele.freeddns.org"
    "router.mdewaele.freeddns.org"
    "nas.mdewaele.freeddns.org"
    "portainer.mdewaele.freeddns.org"

    # Home Automation & IoT Services
    "jellyfin.mdewaele.freeddns.org"
    "zigbee.mdewaele.freeddns.org"
    "unifi.mdewaele.freeddns.org"
    "hotspot.mdewaele.freeddns.org"

    # Media & Entertainment Services
    "media.mdewaele.freeddns.org"
    "api.media.mdewaele.freeddns.org"

    # Static Device Mappings (Yamaha speakers)
    "livingroom.mdewaele.freeddns.org"
    "kitchen.mdewaele.freeddns.org"
    "bureau.mdewaele.freeddns.org"
    "bedroom.mdewaele.freeddns.org"
)

VPN_GATEWAY="***********"
LOCAL_GATEWAY="***********"
WIFI_GATEWAY="***********"  # Assuming WiFi gateway
SYNOLOGY_IP="*************"  # Synology NAS hosting all services
ADGUARD_IP="*************"   # AdGuard Home on Synology NAS
TRAEFIK_IP="*************"   # Traefik on Synology NAS

echo "Testing domains: ${DOMAINS[@]}"
echo "VPN Gateway: $VPN_GATEWAY"
echo "Local Gateway: $LOCAL_GATEWAY"
echo ""

# Function to check VPN connection
check_vpn_connection() {
    echo "=== Checking VPN Connection ==="
    
    # Check if VPN interface exists
    if ip addr show | grep -q "192.168.5."; then
        local vpn_ip=$(ip addr show | grep "192.168.5." | awk '{print $2}' | cut -d'/' -f1)
        echo "✅ VPN interface found with IP: $vpn_ip"
    else
        echo "❌ VPN interface not found - are you connected to WireGuard?"
        return 1
    fi
    
    # Test connectivity to VPN gateway
    if ping -c 3 -W 5 "$VPN_GATEWAY" >/dev/null 2>&1; then
        echo "✅ VPN gateway ($VPN_GATEWAY) is reachable"
    else
        echo "❌ VPN gateway ($VPN_GATEWAY) is not reachable"
        return 1
    fi
    
    # Test connectivity to local gateways
    if ping -c 3 -W 5 "$LOCAL_GATEWAY" >/dev/null 2>&1; then
        echo "✅ Wired LAN gateway ($LOCAL_GATEWAY) is reachable via VPN"
    else
        echo "❌ Wired LAN gateway ($LOCAL_GATEWAY) is not reachable via VPN"
        return 1
    fi

    # Test connectivity to Synology NAS
    if ping -c 3 -W 5 "$SYNOLOGY_IP" >/dev/null 2>&1; then
        echo "✅ Synology NAS ($SYNOLOGY_IP) is reachable via VPN"
    else
        echo "❌ Synology NAS ($SYNOLOGY_IP) is not reachable via VPN"
        return 1
    fi
    
    return 0
}

# Function to test DNS resolution
test_dns_resolution() {
    local domain=$1
    
    echo "Testing DNS resolution for $domain"
    
    if nslookup "$domain" >/dev/null 2>&1; then
        local resolved_ip=$(nslookup "$domain" | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        echo "✅ DNS resolves $domain to $resolved_ip"
        
        # Check if it resolves to internal IP (expected when on VPN)
        if [[ "$resolved_ip" == "*************" ]]; then
            echo "✅ CORRECT: $domain resolves to Synology NAS IP $resolved_ip"
            return 0
        elif [[ "$resolved_ip" == "192.168."* ]]; then
            echo "✅ CORRECT: $domain resolves to internal IP $resolved_ip"
            return 0
        else
            echo "⚠️  WARNING: $domain resolves to external IP $resolved_ip (may still work)"
            return 1
        fi
    else
        echo "❌ DNS resolution failed for $domain"
        return 1
    fi
}

# Function to test web access
test_web_access() {
    local domain=$1
    local protocol=$2
    local expected_status=${3:-200}
    
    echo "Testing $protocol://$domain"
    
    # Test with curl
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 --max-time 15 "$protocol://$domain" 2>/dev/null)
    
    if [ "$http_code" -eq "$expected_status" ] || [ "$http_code" -eq 200 ] || [ "$http_code" -eq 301 ] || [ "$http_code" -eq 302 ]; then
        echo "✅ SUCCESS: $protocol://$domain returned HTTP $http_code"
        return 0
    elif [ "$http_code" -eq 000 ]; then
        echo "❌ FAILED: $protocol://$domain - connection failed"
        return 1
    else
        echo "⚠️  WARNING: $protocol://$domain returned HTTP $http_code"
        return 1
    fi
}

# Function to test service connectivity
test_service_connectivity() {
    local ip=$1
    local port=$2
    local service_name=$3
    
    echo "Testing $service_name connectivity ($ip:$port)"
    
    if command -v nc >/dev/null 2>&1; then
        if nc -z -w5 "$ip" "$port" 2>/dev/null; then
            echo "✅ SUCCESS: $service_name ($ip:$port) is reachable"
            return 0
        else
            echo "❌ FAILED: $service_name ($ip:$port) is not reachable"
            return 1
        fi
    else
        echo "⚠️  WARNING: netcat not available for port testing"
        return 0
    fi
}

# Main testing
echo "=== Starting VPN Access Tests ==="
echo ""

test_failures=0

# Test 1: Check VPN connection
echo "1. Checking VPN connection status..."
if ! check_vpn_connection; then
    echo "❌ VPN connection test failed - cannot proceed with other tests"
    exit 1
fi

echo ""
echo "2. Testing DNS resolution..."

# Test 2: DNS resolution
for domain in "${DOMAINS[@]}"; do
    if ! test_dns_resolution "$domain"; then
        ((test_failures++))
    fi
done

echo ""
echo "3. Testing web service access..."

# Test 3: Web access to services
for domain in "${DOMAINS[@]}"; do
    echo ""
    
    # Test HTTP
    if ! test_web_access "$domain" "http"; then
        ((test_failures++))
    fi
    
    # Test HTTPS
    if ! test_web_access "$domain" "https"; then
        ((test_failures++))
    fi
done

echo ""
echo "4. Testing direct service connectivity..."

# Test 4: Direct service connectivity
if ! test_service_connectivity "$ADGUARD_IP" "3000" "AdGuard Home"; then
    ((test_failures++))
fi

if ! test_service_connectivity "$TRAEFIK_IP" "8080" "Traefik HTTP"; then
    ((test_failures++))
fi

if ! test_service_connectivity "$TRAEFIK_IP" "8443" "Traefik HTTPS"; then
    ((test_failures++))
fi

if ! test_service_connectivity "$TRAEFIK_IP" "9080" "Traefik Dashboard"; then
    ((test_failures++))
fi

if ! test_service_connectivity "$LOCAL_GATEWAY" "443" "EdgeRouter Web UI"; then
    ((test_failures++))
fi

# Test Synology NAS web interface (should be accessible)
if ! test_service_connectivity "$SYNOLOGY_IP" "5000" "Synology DSM HTTP"; then
    ((test_failures++))
fi

if ! test_service_connectivity "$SYNOLOGY_IP" "5001" "Synology DSM HTTPS"; then
    ((test_failures++))
fi

echo ""
echo "5. Testing internet connectivity via VPN..."

# Test 5: Internet access through VPN
if ping -c 3 -W 5 ******* >/dev/null 2>&1; then
    echo "✅ Internet connectivity via VPN is working"
else
    echo "❌ Internet connectivity via VPN is not working"
    ((test_failures++))
fi

echo ""
echo "=== Test Results ==="

if [ $test_failures -eq 0 ]; then
    echo "✅ VPN ACCESS TEST PASSED: All services are accessible via VPN"
    echo "Your VPN setup is working correctly"
else
    echo "❌ VPN ACCESS TEST FAILED: $test_failures issues found"
    echo "Some services are not accessible via VPN - review your configuration"
fi

echo ""
echo "Summary:"
echo "- VPN connection: $([ $test_failures -eq 0 ] && echo "Working" || echo "Issues found")"
echo "- DNS resolution: $([ $test_failures -eq 0 ] && echo "Working" || echo "Issues found")"
echo "- Service access: $([ $test_failures -eq 0 ] && echo "Working" || echo "Issues found")"

echo ""
echo "Next steps:"
echo "1. If test passed: Your setup is complete and secure"
echo "2. If test failed: Check DNS configuration, firewall rules, and service status"
echo "3. Test from different VPN clients to ensure consistency"

exit $test_failures
