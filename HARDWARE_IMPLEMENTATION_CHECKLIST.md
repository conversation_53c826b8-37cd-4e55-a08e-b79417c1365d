# Hardware Implementation Checklist - Professional Home Network

## 🔧 **Pre-Implementation Planning**

### **Hardware Inventory Verification**
- [ ] **EdgeRouter 4** - Primary gateway and VPN server
  - [ ] Power adapter included
  - [ ] Console cable (if needed for recovery)
  - [ ] Ethernet cables for WAN and LAN connections
  
- [ ] **Synology DS923+** - NAS with dual NIC capability
  - [ ] Both network ports functional
  - [ ] Power adapter and drives installed
  - [ ] Initial DSM setup completed
  
- [ ] **USW Flex 2.5G** - Core managed switch
  - [ ] Power adapter (PoE+ injector if needed)
  - [ ] Mounting hardware if rack/wall mounting
  - [ ] Sufficient ports for planned connections
  
- [ ] **24-Port Unmanaged Switch** - Access layer
  - [ ] Power adapter
  - [ ] Sufficient ports for all wired devices
  - [ ] Gigabit capability confirmed
  
- [ ] **4-Port PoE Switch** - UniFi access points
  - [ ] PoE+ capability for UniFi devices
  - [ ] Power adapter with sufficient wattage
  - [ ] Mounting location planned
  
- [ ] **UniFi Access Points**
  - [ ] **UniFi AC Pro** - Primary coverage
  - [ ] **UniFi U6 Lite** - High performance
  - [ ] Mounting hardware and locations planned
  - [ ] PoE power requirements verified

### **Cable Requirements**
- [ ] **Cat6/Cat6a Ethernet Cables**:
  - [ ] 2x cables for DS923+ LAG (ports 2-3 on USW Flex)
  - [ ] 1x cable EdgeRouter to USW Flex (port 1)
  - [ ] 1x cable USW Flex to 24-port switch (port 4)
  - [ ] 1x cable USW Flex to 4-port PoE switch (port 5)
  - [ ] 2x cables for UniFi APs to PoE switch
  - [ ] Additional cables for Yamaha speakers and other devices
  
- [ ] **Cable Management**:
  - [ ] Cable ties or velcro straps
  - [ ] Cable management trays or channels
  - [ ] Labels for cable identification

### **Physical Location Planning**
- [ ] **Network Rack/Cabinet** (if using):
  - [ ] Sufficient rack units for all equipment
  - [ ] Proper ventilation and cooling
  - [ ] Power distribution unit (PDU)
  - [ ] Cable management arms/trays
  
- [ ] **Alternative Mounting**:
  - [ ] Wall mount locations for switches
  - [ ] Shelf space for NAS and router
  - [ ] Ventilation clearance around equipment

## 🏗️ **Phase 1: Physical Infrastructure Setup**

### **Step 1.1: Equipment Placement**
- [ ] **Position EdgeRouter 4**:
  - [ ] Near internet connection point
  - [ ] Good ventilation (fanless design)
  - [ ] Easy access to ports and reset button
  - [ ] Power outlet within reach
  
- [ ] **Position Synology DS923+**:
  - [ ] Central location for optimal network access
  - [ ] Adequate ventilation for cooling fans
  - [ ] UPS power protection recommended
  - [ ] Easy access for drive maintenance
  
- [ ] **Position USW Flex 2.5G**:
  - [ ] Central location to minimize cable runs
  - [ ] Wall mount or rack mount as planned
  - [ ] Power source (PoE injector or direct power)
  - [ ] LED visibility for status monitoring
  
- [ ] **Position Access Switches**:
  - [ ] 24-port switch near wired device concentration
  - [ ] 4-port PoE switch near access point locations
  - [ ] Power outlets for both switches
  - [ ] Cable management space

### **Step 1.2: Initial Power-On Sequence**
**⚠️ Important: Power on devices in this specific order to avoid configuration conflicts**

1. [ ] **EdgeRouter 4 First**:
   - [ ] Connect power adapter
   - [ ] Wait for full boot (LED solid)
   - [ ] Verify WAN connection if available
   
2. [ ] **USW Flex 2.5G Second**:
   - [ ] Connect power (PoE injector or direct)
   - [ ] Wait for boot sequence completion
   - [ ] Verify LED status indicators
   
3. [ ] **Synology DS923+ Third**:
   - [ ] Connect power adapter
   - [ ] Wait for full DSM boot
   - [ ] Verify both NIC ports show link lights
   
4. [ ] **Access Switches Fourth**:
   - [ ] Power on 24-port unmanaged switch
   - [ ] Power on 4-port PoE switch
   - [ ] Verify power LED indicators
   
5. [ ] **UniFi Access Points Last**:
   - [ ] Connect to PoE switch
   - [ ] Wait for boot and adoption readiness
   - [ ] Verify PoE power delivery

## 🔌 **Phase 2: Cable Connections**

### **Step 2.1: Core Infrastructure Connections**
**⚠️ Make connections with devices powered off when possible**

- [ ] **EdgeRouter 4 Connections**:
  - [ ] WAN (eth0): Connect to internet modem/ONT
  - [ ] LAN (eth1): Connect to USW Flex port 1
  - [ ] Verify link lights on both ends
  
- [ ] **USW Flex 2.5G Connections**:
  - [ ] Port 1: From EdgeRouter 4 LAN
  - [ ] Port 2: To DS923+ NIC 1 (LAG Member 1)
  - [ ] Port 3: To DS923+ NIC 2 (LAG Member 2)
  - [ ] Port 4: To 24-port switch uplink
  - [ ] Port 5: To 4-port PoE switch uplink
  - [ ] Verify all link lights active

### **Step 2.2: DS923+ Dual NIC Connection**
- [ ] **Physical Connections**:
  - [ ] NIC 1 (LAN 1): Cable to USW Flex port 2
  - [ ] NIC 2 (LAN 2): Cable to USW Flex port 3
  - [ ] Use identical cable types and lengths
  - [ ] Verify both ports show link lights
  
- [ ] **Cable Quality Verification**:
  - [ ] Use Cat6 or better cables
  - [ ] Test cable continuity if available
  - [ ] Ensure secure connections at both ends
  - [ ] Label cables for future reference

### **Step 2.3: Access Layer Connections**
- [ ] **24-Port Switch**:
  - [ ] Uplink from USW Flex port 4
  - [ ] Connect Yamaha speakers
  - [ ] Connect other wired devices as needed
  - [ ] Leave ports available for future expansion
  
- [ ] **4-Port PoE Switch**:
  - [ ] Uplink from USW Flex port 5
  - [ ] UniFi AC Pro to port 1
  - [ ] UniFi U6 Lite to port 2
  - [ ] Verify PoE power delivery (LED indicators)
  - [ ] Reserve ports 3-4 for future APs

## 📡 **Phase 3: Access Point Installation**

### **Step 3.1: UniFi AC Pro Setup**
- [ ] **Physical Installation**:
  - [ ] Mount in optimal location for coverage
  - [ ] Connect PoE cable from 4-port switch
  - [ ] Verify power LED and network connectivity
  - [ ] Test initial wireless broadcast
  
- [ ] **Coverage Verification**:
  - [ ] Walk test with mobile device
  - [ ] Check signal strength in all areas
  - [ ] Identify any dead zones
  - [ ] Document optimal placement

### **Step 3.2: UniFi U6 Lite Setup**
- [ ] **Physical Installation**:
  - [ ] Mount for complementary coverage to AC Pro
  - [ ] Connect PoE cable from 4-port switch
  - [ ] Verify WiFi 6 capability active
  - [ ] Test high-performance device connectivity
  
- [ ] **Performance Verification**:
  - [ ] Speed test with WiFi 6 capable device
  - [ ] Verify 5GHz band performance
  - [ ] Test multiple device connections
  - [ ] Document performance baselines

## 🔍 **Phase 4: Initial Connectivity Verification**

### **Step 4.1: Physical Layer Verification**
- [ ] **Link Light Verification**:
  - [ ] EdgeRouter 4: WAN and LAN ports active
  - [ ] USW Flex: All 5 ports showing link lights
  - [ ] DS923+: Both NIC ports active
  - [ ] 24-port switch: Uplink and device ports active
  - [ ] 4-port PoE switch: All connected ports active
  
- [ ] **Power Status Check**:
  - [ ] All devices powered and stable
  - [ ] PoE delivery confirmed for UniFi APs
  - [ ] No overheating or unusual fan noise
  - [ ] LED status indicators normal

### **Step 4.2: Basic Network Connectivity**
- [ ] **EdgeRouter 4 Access**:
  - [ ] Web interface accessible at ***********
  - [ ] SSH access working (if enabled)
  - [ ] Internet connectivity confirmed
  - [ ] DHCP serving basic addresses
  
- [ ] **Synology DS923+ Access**:
  - [ ] DSM accessible via web interface
  - [ ] Both network interfaces detected
  - [ ] Basic network connectivity confirmed
  - [ ] Ready for LAG configuration

### **Step 4.3: Switch Functionality**
- [ ] **USW Flex 2.5G**:
  - [ ] Management interface accessible
  - [ ] All ports operational
  - [ ] Ready for VLAN configuration
  - [ ] LAG capability confirmed
  
- [ ] **Access Switches**:
  - [ ] 24-port switch passing traffic
  - [ ] 4-port PoE switch delivering power
  - [ ] All connected devices getting connectivity
  - [ ] No port errors or issues

## 📋 **Phase 5: Pre-Configuration Documentation**

### **Step 5.1: Network Documentation**
- [ ] **Physical Topology Map**:
  - [ ] Document all cable connections
  - [ ] Record port assignments
  - [ ] Note cable types and lengths
  - [ ] Create visual network diagram
  
- [ ] **Device Inventory**:
  - [ ] Record all MAC addresses
  - [ ] Document serial numbers
  - [ ] Note firmware versions
  - [ ] Create device location map

### **Step 5.2: Baseline Performance**
- [ ] **Speed Tests**:
  - [ ] Internet connection speed
  - [ ] Local network performance
  - [ ] WiFi performance by location
  - [ ] Document baseline metrics
  
- [ ] **Connectivity Matrix**:
  - [ ] Test device-to-device connectivity
  - [ ] Verify internet access from all devices
  - [ ] Document any connectivity issues
  - [ ] Prepare for configuration phase

## ✅ **Hardware Setup Completion Checklist**

### **Final Verification**
- [ ] All devices powered and stable
- [ ] All network connections active
- [ ] Basic connectivity confirmed
- [ ] No hardware errors or issues
- [ ] Documentation completed
- [ ] Ready for network configuration phase

### **Next Steps**
- [ ] Proceed to network configuration (Phase 2 of main guide)
- [ ] Apply EdgeRouter configuration
- [ ] Configure Synology LAG
- [ ] Set up VLANs and security rules

**🎯 Hardware setup is complete when all devices are physically connected, powered, and showing basic connectivity. The network is now ready for software configuration and optimization.**
