# DS923+ Dual NIC Optimization - Enhanced VPN-Only Access Configuration

## Executive Summary

Your Synology DS923+ having 2 network ports (dual NICs) significantly enhances the optimization opportunities. This allows for advanced configurations including network bonding, traffic separation, and improved redundancy/performance.

## 🚀 **DS923+ Dual NIC Capabilities**

### **Hardware Specifications**
- **Port 1**: 1 Gigabit Ethernet (RJ-45)
- **Port 2**: 1 Gigabit Ethernet (RJ-45)
- **Total Potential**: Up to 2 Gbps aggregate bandwidth
- **Bonding Support**: Link Aggregation (802.3ad), Load Balancing, Failover

### **Optimization Opportunities**
1. **Link Aggregation (LAG)**: Combine both ports for 2 Gbps bandwidth
2. **Traffic Separation**: Dedicate ports for different traffic types
3. **Network Redundancy**: Failover protection for critical services
4. **VLAN Separation**: Direct connection to different network segments

## 🎯 **Recommended Configurations**

### **Option 1: Link Aggregation (LAG) - RECOMMENDED** ⭐

#### **Configuration Benefits**
- **2 Gbps Aggregate Bandwidth**: Double the throughput to NAS
- **Load Balancing**: Automatic traffic distribution across both ports
- **Redundancy**: Automatic failover if one port fails
- **Simplified Management**: Single logical interface

#### **Updated Physical Topology**
```
Internet
    │
EdgeRouter 4 (***********)
    │ eth1 (Trunk - All VLANs)
    │
USW Flex 2.5G (***********) - Core Managed Switch
    ├── Port 1: 2.5G → Reserved for future expansion
    ├── Port 2: 1G → Synology NAS Port 1 (LAG Member 1)
    ├── Port 3: 1G → Synology NAS Port 2 (LAG Member 2)
    ├── Port 4: 1G → 24-Port Unmanaged Switch (uplink)
    └── Port 5: 1G → 4-Port PoE Switch (uplink)
    
Synology DS923+ (*************)
    ├── Port 1: 1G → USW Flex Port 2 (LAG Member 1)
    ├── Port 2: 1G → USW Flex Port 3 (LAG Member 2)
    └── LAG Interface: 2G aggregate bandwidth
```

#### **LAG Configuration Steps**
```bash
# On Synology DSM:
1. Control Panel → Network → Network Interface
2. Create → Create Bond
3. Select both network interfaces
4. Bond Type: "802.3ad Dynamic Link Aggregation"
5. IP: *************/24
6. Gateway: ***********

# On USW Flex 2.5G:
1. Configure Port Profile: "Link Aggregation"
2. Add Ports 2 and 3 to LAG group
3. Set LAG as Access port VLAN 1
```

### **Option 2: Traffic Separation** 

#### **Configuration Benefits**
- **Dedicated Media Traffic**: One port for media streaming/DLNA
- **Dedicated Management**: One port for admin and general services
- **Traffic Isolation**: Separate high-bandwidth from management traffic
- **Network Segmentation**: Direct connection to different VLANs

#### **Updated Physical Topology**
```
USW Flex 2.5G (***********)
    ├── Port 1: 2.5G → Future expansion
    ├── Port 2: 1G → Synology NAS Port 1 (VLAN 1 - Management)
    ├── Port 3: 1G → Synology NAS Port 2 (VLAN 3 - Media)
    ├── Port 4: 1G → 24-Port Switch (uplink)
    └── Port 5: 1G → 4-Port PoE Switch (uplink)
    
Synology DS923+ Dual Interface:
    ├── eth0 (Port 1): ************* - Management & Services
    ├── eth1 (Port 2): 192.168.3.113 - Media & DLNA Traffic
    └── Traffic automatically routed based on destination
```

### **Option 3: Redundancy + Performance**

#### **Configuration Benefits**
- **Active-Backup Failover**: Automatic failover for reliability
- **Performance Optimization**: Primary port optimized for speed
- **Zero Downtime**: Seamless failover during maintenance
- **Simple Configuration**: Easy to set up and maintain

## 📊 **Performance Comparison**

### **Current Single NIC vs Dual NIC Options**

| Configuration | Bandwidth | Redundancy | Complexity | Recommended Use |
|---------------|-----------|------------|------------|-----------------|
| **Single 2.5G** | 2.5 Gbps | None | Low | Current setup |
| **LAG (2x1G)** | 2.0 Gbps | High | Medium | **RECOMMENDED** |
| **Traffic Separation** | 1G per function | Medium | High | Specialized needs |
| **Active-Backup** | 1 Gbps | High | Low | Reliability focus |

## 🔧 **Updated USW Flex 2.5G Port Allocation**

### **Recommended LAG Configuration**
```
Port Allocation with DS923+ LAG:
├── Port 1: 2.5G → Future high-bandwidth device
├── Port 2: 1G → NAS Port 1 (LAG Member 1)
├── Port 3: 1G → NAS Port 2 (LAG Member 2)
├── Port 4: 1G → 24-Port Switch (wired devices)
└── Port 5: 1G → 4-Port PoE Switch (UniFi APs)
```

### **Performance Benefits**
- **NAS Throughput**: 2 Gbps aggregate (vs 1G single port)
- **Redundancy**: Automatic failover protection
- **Load Distribution**: Traffic balanced across both links
- **Future Expansion**: 2.5G port available for next-gen devices

## 🛠️ **Implementation Guide**

### **Phase 1: Configure Link Aggregation**

#### **1.1 Synology DSM Configuration**
```bash
# Access DSM Web Interface
1. Control Panel → Network → Network Interface
2. Click "Create" → "Create Bond"
3. Select both network interfaces (LAN 1 and LAN 2)
4. Bond Configuration:
   - Bond Type: "802.3ad Dynamic Link Aggregation"
   - IP Address: *************
   - Subnet Mask: *************
   - Gateway: ***********
   - DNS: *************, *******
5. Apply settings and test connectivity
```

#### **1.2 USW Flex 2.5G Configuration**
```bash
# Configure LAG on USW Flex
1. Access UniFi Controller
2. Navigate to USW Flex device settings
3. Configure Port Profiles:
   - Create "NAS_LAG" profile
   - Add Ports 2 and 3 to LAG group
   - Set as Access port VLAN 1
   - Enable LACP (802.3ad)
4. Apply configuration and verify link status
```

### **Phase 2: Performance Testing**

#### **2.1 Bandwidth Testing**
```bash
# Test aggregate bandwidth
# From multiple clients simultaneously:
iperf3 -c ************* -t 60 -P 4

# Expected results:
# - Single client: ~950 Mbps per connection
# - Multiple clients: Up to 1.9 Gbps aggregate
# - Failover test: Disconnect one cable, verify continued operation
```

#### **2.2 DLNA Performance Testing**
```bash
# Test media streaming performance
# Multiple Yamaha speakers streaming simultaneously
# Monitor network utilization on both LAG members
# Verify load balancing across both links
```

## 🚀 **Expected Performance Improvements**

### **Quantified Benefits**
- **NAS Throughput**: 100% improvement (1G → 2G aggregate)
- **Media Streaming**: Support for more concurrent streams
- **Backup Performance**: Faster network backups and sync
- **Multi-client Access**: Better performance with multiple users
- **Redundancy**: Zero downtime during single port failure

### **Real-World Scenarios**
```
Performance Scenarios:
├── Single large file transfer: ~1.9 Gbps
├── Multiple client access: Load balanced across both ports
├── Media streaming + backup: Concurrent without bottleneck
├── Container services: Better response under load
└── VPN access: Improved performance for remote users
```

## 🔌 **Updated Physical Connections**

### **Step-by-Step Wiring (LAG Configuration)**
```bash
1. EdgeRouter eth1 → USW Flex uplink port
2. USW Flex Port 2 → Synology NAS Port 1
3. USW Flex Port 3 → Synology NAS Port 2
4. USW Flex Port 4 → 24-Port Switch uplink
5. USW Flex Port 5 → 4-Port PoE Switch uplink
6. Configure LAG on both Synology and USW Flex
7. Test connectivity and performance
```

### **Cable Requirements**
- **2x Cat6**: USW Flex to NAS (LAG members)
- **2x Cat6**: USW Flex to other switches
- **1x Cat6a**: EdgeRouter to USW Flex (future-proof)

## 🛡️ **Security Considerations**

### **LAG Security Benefits**
- **No Security Changes**: Same firewall rules apply
- **Redundant Connectivity**: Improved availability for security services
- **Load Distribution**: Better performance for security scanning
- **Failover Protection**: Continued security monitoring during failures

### **Network Monitoring**
```bash
# Monitor LAG performance
# On Synology:
cat /proc/net/bonding/bond0

# On UniFi Controller:
# Check port statistics and LAG status
# Monitor traffic distribution across LAG members
```

## 🎯 **Recommendation Summary**

### **Strongly Recommended: Link Aggregation (LAG)**
1. **Best Performance**: 2 Gbps aggregate bandwidth
2. **High Reliability**: Automatic failover protection
3. **Simple Management**: Single logical interface
4. **Future-Proof**: Maintains 2.5G port for expansion
5. **Cost-Effective**: Uses existing hardware optimally

### **Implementation Priority**
1. **Phase 1**: Configure LAG between NAS and USW Flex
2. **Phase 2**: Test performance and failover
3. **Phase 3**: Monitor and optimize traffic patterns
4. **Phase 4**: Plan future use of available 2.5G port

This dual NIC configuration significantly enhances your network performance while maintaining the VPN-only security model and providing excellent redundancy for your critical services.
