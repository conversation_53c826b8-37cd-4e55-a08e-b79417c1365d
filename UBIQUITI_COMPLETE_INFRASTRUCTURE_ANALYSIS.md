# Complete Ubiquiti Infrastructure - VPN-Only Access Optimization

## Executive Summary

After identifying your complete network infrastructure including the 24-port unmanaged switch and 4-port PoE switch, I've revised the optimization to properly leverage all components while simplifying the configuration and removing unnecessary complexity.

## 🏗️ **Complete Hardware Inventory**

### **Network Infrastructure**
- **EdgeRouter 4**: Primary gateway and VPN server (***********)
- **USW Flex 2.5G**: 5-port managed switch - Core/Distribution layer
- **24-Port Unmanaged Switch**: Access layer for general wired devices
- **4-Port PoE Switch**: Dedicated for UniFi access points
- **Synology NAS**: All services host (***********13)

### **WiFi Infrastructure**
- **UniFi AC Pro**: Primary WiFi access point (WiFi 5)
- **UniFi U6 Lite**: Secondary WiFi access point (WiFi 6)
- **UniFi Network Controller**: Running on Synology NAS

## 🎯 **Optimized Physical Topology**

### **Recommended Switch Connection Strategy**
```
Internet
    │
EdgeRouter 4 (***********)
    │ eth1 (Trunk - All VLANs)
    │
USW Flex 2.5G (***********) - Core Managed Switch
    ├── Port 1: 2.5G → Synology NAS (***********13)
    ├── Port 2: 1G → 24-Port Unmanaged Switch (uplink)
    ├── Port 3: 1G → 4-Port PoE Switch (uplink)
    ├── Port 4: 1G → High-priority wired devices
    └── Port 5: 2.5G → Future expansion
    
24-Port Unmanaged Switch (***********)
    ├── Port 1: From USW Flex (uplink)
    ├── Ports 2-24: General wired devices
    └── All devices on VLAN 1 (***********/24)
    
4-Port PoE Switch (***********)
    ├── Port 1: From USW Flex (uplink)
    ├── Port 2: UniFi AC Pro (192.168.1.3) + PoE
    ├── Port 3: UniFi U6 Lite (192.168.1.4) + PoE
    └── Port 4: Future PoE device
```

### **Why This Topology is Optimal**
1. **USW Flex 2.5G as Core**: Handles VLAN routing and high-bandwidth NAS connection
2. **24-Port for Distribution**: Provides ample ports for wired devices without VLAN complexity
3. **4-Port PoE Dedicated**: Clean power delivery to UniFi devices
4. **Simplified VLAN Strategy**: Only managed switch handles VLANs, unmanaged switches carry all traffic

## 📋 **Simplified Network Segmentation**

### **Revised VLAN Strategy**
Since you have unmanaged switches, we'll simplify the VLAN approach:

```
VLAN 1 (***********/24) - Core Infrastructure & Wired Devices
├── EdgeRouter 4: ***********
├── USW Flex 2.5G: ***********
├── UniFi AC Pro: 192.168.1.3
├── UniFi U6 Lite: 192.168.1.4
├── 24-Port Switch: ***********
├── 4-Port PoE Switch: ***********
├── Synology NAS: ***********13 (2.5G connection)
└── All wired devices: ***********0-100

VLAN 2 (172.16.1.0/24) - Guest Network (WiFi only)
├── Guest WiFi clients from both APs
└── Complete isolation from internal networks

VLAN 3 (192.168.3.0/24) - Media Network (WiFi + Wired)
├── Yamaha Speakers: 192.168.3.10-13 (wired via 24-port switch)
├── Media WiFi clients
└── DLNA optimized with multicast support

VLAN 4 (192.168.4.0/24) - IoT Trusted (WiFi only)
├── Smart Thermostat: 192.168.4.10
├── Air Quality Meter: 192.168.4.11
└── Home Assistant access only

VLAN 5 (192.168.5.0/24) - VPN Network
├── VPN Gateway: 192.168.5.1
└── VPN clients: 192.168.5.2+

VLAN 6 (192.168.6.0/24) - IoT Untrusted (WiFi only)
├── Generic smart devices
└── Internet-only access

VLAN 10 (192.168.10.0/24) - Primary WiFi Users
├── General WiFi devices
└── Full internal network access
```

## 🔧 **Simplified Configuration Approach**

### **Key Simplifications**
1. **Wired Devices**: All on VLAN 1 (no complex wired VLAN segmentation)
2. **WiFi Segmentation**: VLANs only for WiFi networks where needed
3. **Media Devices**: Yamaha speakers on wired VLAN 3 (tagged through USW Flex)
4. **Management**: All infrastructure devices on VLAN 1 for easy management

### **USW Flex 2.5G Port Configuration**
```
Port 1 (2.5G): Access VLAN 1 → Synology NAS
Port 2 (1G): Trunk VLANs 1,3 → 24-Port Switch
Port 3 (1G): Trunk All VLANs → 4-Port PoE Switch
Port 4 (1G): Access VLAN 1 → High-priority wired devices
Port 5 (2.5G): Trunk All VLANs → Future expansion
```

### **WiFi Network Strategy**
```
UniFi AC Pro (Primary Coverage):
├── HomeNetwork → VLAN 10 (Primary users)
├── HomeGuest → VLAN 2 (Guest isolation)
└── IoT-Trusted → VLAN 4 (Smart home devices)

UniFi U6 Lite (High Performance):
├── HomeNetwork-6 → VLAN 10 (WiFi 6 users)
├── Media-6 → VLAN 3 (Media streaming)
├── IoT-Untrusted → VLAN 6 (Generic IoT)
└── HomeGuest → VLAN 2 (Guest isolation)
```

## 🚀 **Performance Optimizations**

### **2.5Gbps Utilization Strategy**
- **Port 1**: Synology NAS (maximum throughput for media/backups)
- **Port 5**: Reserved for future high-bandwidth device or uplink

### **Traffic Flow Optimization**
1. **Media Streaming**: Direct 2.5G path from NAS to clients
2. **Wired Devices**: Full gigabit through 24-port switch
3. **WiFi Performance**: Dedicated high-performance networks
4. **PoE Efficiency**: Dedicated switch for clean power delivery

## 🛡️ **Security Implementation**

### **Simplified Security Model**
```
Network Access Control:
├── VLAN 1 (Wired): Full internal access
├── VLAN 10 (WiFi Users): Full internal access
├── VLAN 3 (Media): Media services + internet
├── VLAN 4 (IoT Trusted): Home Assistant only
├── VLAN 6 (IoT Untrusted): Internet only
├── VLAN 2 (Guest): Internet only
└── VLAN 5 (VPN): Full access to all networks
```

### **Firewall Rules Simplification**
- **Wired devices**: Trusted (VLAN 1) - full access
- **WiFi segmentation**: Based on device type and trust level
- **Guest isolation**: Complete separation from internal networks
- **VPN access**: Full administrative access to all networks

## 📊 **Implementation Benefits**

### **Simplified Management**
- **Single Managed Switch**: Only USW Flex handles VLANs
- **Unmanaged Switches**: Simple plug-and-play operation
- **Reduced Complexity**: Fewer VLAN configurations to maintain
- **Easy Troubleshooting**: Clear separation of managed vs unmanaged infrastructure

### **Performance Benefits**
- **2.5G NAS Connection**: Maximum throughput for media and backups
- **Dedicated PoE**: Clean power delivery to access points
- **Ample Ports**: 24 ports for wired device expansion
- **Optimized WiFi**: Specialized networks for different use cases

### **Cost Efficiency**
- **Leverages Existing Hardware**: Uses all current equipment optimally
- **No Additional Hardware**: Works with current switch infrastructure
- **Future-Proof**: 2.5G port available for expansion

## 🔌 **Physical Connection Guide**

### **Step-by-Step Wiring**
1. **EdgeRouter to USW Flex**: eth1 → Port 1 (or designated uplink)
2. **USW Flex to NAS**: Port 1 (2.5G) → NAS 2.5G port
3. **USW Flex to 24-Port**: Port 2 (1G) → 24-Port uplink
4. **USW Flex to 4-Port PoE**: Port 3 (1G) → 4-Port uplink
5. **4-Port PoE to APs**: Port 2 → AC Pro, Port 3 → U6 Lite
6. **Wired Devices**: Connect to 24-Port switch (Ports 2-24)

### **Cable Requirements**
- **1x Cat6a/Cat7**: EdgeRouter to USW Flex (future-proof)
- **1x Cat6a**: USW Flex to NAS (2.5G capable)
- **2x Cat6**: USW Flex to other switches
- **2x Cat6**: PoE switch to access points
- **Standard Cat5e/Cat6**: All other connections

## 🎯 **Expected Results**

### **Performance Improvements**
- **NAS Throughput**: 2.5x improvement (1G → 2.5G)
- **Network Simplicity**: Reduced configuration complexity
- **WiFi Performance**: Optimized networks for different device types
- **Scalability**: Easy addition of wired devices

### **Management Benefits**
- **Single Point of VLAN Control**: Only USW Flex manages VLANs
- **UniFi Integration**: Centralized WiFi management
- **Simplified Troubleshooting**: Clear network hierarchy
- **Future Expansion**: Ready for additional high-speed devices

This optimized configuration leverages your complete switch infrastructure while maintaining simplicity and maximizing performance. The approach recognizes that unmanaged switches work best when kept simple, while the managed USW Flex handles the complex VLAN routing and high-performance connections.
