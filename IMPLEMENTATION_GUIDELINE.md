# Home Network Implementation Guideline - VPN-Only Access with DS923+ Dual NIC LAG

## 🎯 **Implementation Overview**

This guideline provides a step-by-step approach to implementing your professional-grade home network with VPN-only access, 
featuring Synology DS923+ dual NIC Link Aggregation and complete Ubiquity infrastructure.

## 📋 **Prerequisites & Planning**

### **Required Hardware**
- ✅ EdgeRouter 4 (Primary gateway & VPN server)
- ✅ Synology DS923+ NAS (Dual NIC capable)
- ✅ USW Flex 2.5G (Core managed switch)
- ✅ 24-Port Unmanaged Switch (Access layer)
- ✅ 4-Port PoE Switch (UniFi APs)
- ✅ UniFi AC Pro (WiFi 5 coverage)
- ✅ UniFi U6 Lite (WiFi 6 performance)
- ✅ Yamaha MusicCast speakers (DLNA capable)

### **Network Planning**
- **Primary Network**: 192.168.1.0/24 (Core infrastructure)
- **VPN Network**: 192.168.5.0/24 (Administrative access)
- **Guest Network**: 172.16.1.0/24 (Isolated)
- **Media Network**: 192.168.3.0/24 (DLNA optimized)
- **IoT Networks**: 192.168.4.0/24 (trusted), 192.168.6.0/24 (untrusted)
- **WiFi Users**: 192.168.10.0/24 (Full access)

## 🔧 **Phase 1: Hardware Setup & Physical Connections**

### **Step 1.1: Cable Infrastructure**
```
Internet → EdgeRouter 4 (eth0) → USW Flex 2.5G (Port 1)
                                      ├── Port 2: DS923+ NIC 1 (LAG Member 1)
                                      ├── Port 3: DS923+ NIC 2 (LAG Member 2)
                                      ├── Port 4: 24-Port Switch (Wired devices)
                                      └── Port 5: 4-Port PoE Switch (UniFi APs)
```

**Physical Connection Checklist:**
- [x] Connect internet to EdgeRouter 4 WAN port
- [x] Connect EdgeRouter 4 LAN (eth1) to USW Flex port 1
- [x] Connect DS923+ NIC 1 to USW Flex port 2
- [x] Connect DS923+ NIC 2 to USW Flex port 3
- [x] Connect 24-port switch to USW Flex port 4
- [x] Connect 4-port PoE switch to USW Flex port 5
- [x] Connect UniFi APs to 4-port PoE switch
- [x] Connect Yamaha speakers to 24-port switch

### **Step 1.2: Power & Initial Boot**
- [x] Power on EdgeRouter 4 first
- [x] Power on USW Flex 2.5G
- [x] Power on Synology DS923+
- [x] Power on switches and access points
- [x] Verify all devices have power and link lights

## 🖥️ **Phase 2: Core Network Configuration**

### **Step 2.1: EdgeRouter 4 Basic Setup**
1. **Initial Access**: Connect to *********** via web interface
2. **Run Setup Wizard**: Configure basic internet connection
3. **Update Firmware**: Ensure latest EdgeOS version
4. **Enable SSH**: For advanced configuration

### **Step 2.2: Synology DS923+ Link Aggregation**
1. **Access DSM**: Connect to Synology via web interface
2. **Network Configuration**:
   - Go to Control Panel → Network → Network Interface
   - Select "Create" → "Create Bond"
   - Choose both network interfaces (LAN 1 & LAN 2)
   - Select "Link Aggregation" mode
   - Configure IP: *************/24
   - Gateway: ***********
   - DNS: ***********

### **Step 2.3: USW Flex 2.5G Configuration**
1. **Adopt Switch**: Use UniFi Controller or standalone mode
2. **Configure LAG**:
   - Create port profile for LAG (ports 2-3)
   - Set to "All" networks or trunk mode
   - Enable Link Aggregation on switch side

## 🔒 **Phase 3: Network Segmentation & VLANs**

### **Step 3.1: Apply EdgeRouter Configuration**
```bash
# Apply the complete network configuration
sudo bash optimization/ds923-dual-nic-topology.conf
```

**This configuration includes:**
- [x] All 7 VLAN definitions
- [x] DHCP pools for each network
- [x] Static IP reservations
- [x] NAT and routing rules
- [x] Basic firewall rules

### **Step 3.2: Configure UniFi Controller**
1. **Install Controller**: On Synology NAS via Docker
2. **Import Configuration**: Use `optimization/unifi-controller-config.json`
3. **Network Profiles**:
   - [x] Guest Network (VLAN 2)
   - [x] Media Network (VLAN 3)
   - [x] IoT Trusted (VLAN 4)
   - [x] IoT Untrusted (VLAN 6)
   - [x] WiFi Users (VLAN 10)

## 🛡️ **Phase 4: Security Implementation**

### **Step 4.1: Apply Enhanced Firewall Rules**
```bash
# Apply comprehensive security rules
sudo bash optimization/ubiquiti-firewall-rules.conf
```

**Security Features:**
- [ ] Block all WAN access except WireGuard (port 51820)
- [ ] Inter-VLAN access control
- [ ] Guest network isolation
- [ ] IoT device restrictions
- [ ] VPN-only administrative access

### **Step 4.2: WireGuard VPN Setup**
1. **Generate Keys**: Use provided scripts in `clients/`
2. **Configure Server**: Apply WireGuard configuration
3. **Create Client Configs**: Desktop and mobile profiles
4. **Test VPN Access**: Verify connectivity to all services

## 🌐 **Phase 5: Services & DNS Configuration**

### **Step 5.1: DNS Setup**
1. **AdGuard Home**: Install on Synology NAS
2. **Configure DNS Rewrites**:
   - home.mdewaele.freeddns.org → *************
   - nas.mdewaele.freeddns.org → *************
   - router.mdewaele.freeddns.org → ***********
3. **EdgeRouter DNS**: Point to AdGuard (*************)

### **Step 5.2: Traefik Reverse Proxy**
1. **Install Traefik**: Using Docker Compose on Synology
2. **Configure Ports**: 8080 (HTTP), 8443 (HTTPS), 9080 (Dashboard)
3. **SSL Certificates**: Set up Let's Encrypt or self-signed
4. **Service Discovery**: Configure for containerized services

## 🧪 **Phase 6: Testing & Validation**

### **Step 6.1: Network Connectivity Tests**
```bash
# Run comprehensive test suite
bash testing/ds923-lag-comprehensive-test.sh
```

**Test Coverage:**
- [ ] LAG performance and failover
- [ ] VLAN isolation and routing
- [ ] DNS resolution and rewrites
- [ ] VPN connectivity
- [ ] Service accessibility

### **Step 6.2: Performance Validation**
- [ ] **LAG Throughput**: Test 2 Gbps aggregate bandwidth
- [ ] **DLNA Streaming**: Verify Yamaha speaker connectivity
- [ ] **Multi-client Performance**: Test concurrent access
- [ ] **Failover Testing**: Disconnect one LAG member

### **Step 6.3: Security Verification**
- [ ] **External Access Blocking**: Verify all services blocked from WAN
- [ ] **VPN-Only Access**: Confirm services only accessible via VPN
- [ ] **Network Isolation**: Test guest and IoT network separation
- [ ] **Firewall Rules**: Validate inter-VLAN restrictions

## 📊 **Phase 7: Monitoring & Optimization**

### **Step 7.1: Performance Monitoring**
- **LAG Status**: Monitor bond interface health
- **Network Utilization**: Track bandwidth usage
- **Service Availability**: Monitor all containerized services
- **WiFi Performance**: Use UniFi Controller analytics

### **Step 7.2: Security Monitoring**
- **Firewall Logs**: Regular review of blocked connections
- **VPN Access**: Monitor VPN client connections
- **Intrusion Detection**: Set up alerts for suspicious activity
- **Certificate Management**: Monitor SSL certificate expiration

## 🔧 **Maintenance Schedule**

### **Regular Tasks**
- **Daily**: Check service status and connectivity
- **Weekly**: Review LAG performance and failover capability
- **Monthly**: Update firmware and security patches
- **Quarterly**: Complete security audit and performance review
- **Annually**: Full configuration backup and disaster recovery test

## 📞 **Troubleshooting Resources**

### **Common Issues & Solutions**
1. **LAG Not Working**: Check switch port configuration and cable connections
2. **VPN Access Issues**: Verify firewall rules and client configuration
3. **DNS Resolution Problems**: Check AdGuard Home and EdgeRouter DNS settings
4. **Service Port Conflicts**: Review Traefik and Synology port assignments
5. **WiFi Network Issues**: Use UniFi Controller diagnostics

### **Support Documentation**
- **Configuration Files**: All settings documented in repository
- **Test Scripts**: Automated validation tools available
- **Network Diagrams**: Visual reference for troubleshooting
- **Performance Baselines**: Expected metrics for comparison

## 🎯 **Success Criteria**

### **Performance Goals**
- ✅ 2 Gbps aggregate NAS bandwidth
- ✅ <3 second LAG failover time
- ✅ 8+ concurrent 4K media streams
- ✅ 30% WiFi performance improvement

### **Security Goals**
- ✅ Zero external service access (except VPN)
- ✅ Complete network segmentation
- ✅ VPN-only administrative access
- ✅ Guest and IoT isolation

### **Functionality Goals**
- ✅ All services accessible via VPN
- ✅ DLNA streaming to Yamaha speakers
- ✅ Seamless failover capability
- ✅ Centralized WiFi management

This implementation guideline provides a structured approach to building your professional-grade home network with enterprise-level security and performance.
