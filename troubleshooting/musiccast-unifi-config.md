# UniFi Controller Configuration for MusicCast Cross-VLAN Discovery

## Overview
This guide configures the UniFi Controller to optimize MusicCast device discovery and streaming across VLANs.

## Required UniFi Controller Settings

### 1. Network Configuration (Settings → Networks)

#### Media Network (VLAN 3) Settings:
- **Name**: Media Network
- **VLAN ID**: 3
- **Gateway/Subnet**: 192.168.3.1/24
- **DHCP Mode**: None (handled by <PERSON><PERSON><PERSON><PERSON>)
- **IGMP Snooping**: ✅ **ENABLED**
- **Multicast Enhancement**: ✅ **ENABLED** 
- **mDNS**: ✅ **ENABLED**

#### WiFi Users Network (VLAN 10) Settings:
- **Name**: WiFi Users
- **VLAN ID**: 10
- **Gateway/Subnet**: 192.168.10.1/24
- **DHCP Mode**: None (handled by EdgeRouter)
- **IGMP Snooping**: ✅ **ENABLED**
- **Multicast Enhancement**: ✅ **ENABLED**
- **mDNS**: ✅ **ENABLED**

#### Core Network (VLAN 1) Settings:
- **Name**: Core Infrastructure
- **VLAN ID**: 1 (Native)
- **Gateway/Subnet**: 192.168.1.1/24
- **DHCP Mode**: None (handled by EdgeRouter)
- **IGMP Snooping**: ✅ **ENABLED**
- **Multicast Enhancement**: ✅ **ENABLED**
- **mDNS**: ✅ **ENABLED**

### 2. WiFi Network Configuration (Settings → WiFi)

#### Media-6 WiFi Network (for MusicCast speakers):
```json
{
  "name": "Media-6",
  "enabled": true,
  "security": "wpapsk",
  "wpa_mode": "wpa2",
  "wpa_enc": "ccmp",
  "passphrase": "YOUR_MEDIA_PASSWORD",
  "vlan": 3,
  "vlan_enabled": true,
  "hide_ssid": false,
  "mac_filter_enabled": false,
  "radius_mac_auth_enabled": false,
  "schedule_enabled": false,
  "multicast_enhance": true,
  "proxy_arp": false,
  "dhcp_option_82": false,
  "l2_isolation": false,
  "dtim_mode": "default",
  "dtim_na": 1,
  "dtim_ng": 1,
  "minrate_na_enabled": false,
  "minrate_ng_enabled": false,
  "bc_filter_enabled": false,
  "group_rekey": 3600,
  "fast_roaming_enabled": true,
  "pmf_mode": "optional",
  "wpa3_support": true,
  "wpa3_transition": true
}
```

#### HomeNetwork WiFi (for mobile devices):
```json
{
  "name": "HomeNetwork",
  "enabled": true,
  "security": "wpapsk",
  "wpa_mode": "wpa2",
  "wpa_enc": "ccmp",
  "passphrase": "YOUR_HOME_PASSWORD",
  "vlan": 10,
  "vlan_enabled": true,
  "hide_ssid": false,
  "multicast_enhance": true,
  "proxy_arp": false,
  "l2_isolation": false,
  "fast_roaming_enabled": true,
  "pmf_mode": "optional",
  "wpa3_support": true,
  "wpa3_transition": true
}
```

### 3. Access Point Configuration

#### UniFi U6 Lite (Media Network AP):
- **Location**: High-performance areas
- **Networks**: Media-6, HomeNetwork-6, IoT-Untrusted, HomeGuest
- **Channel Width 2.4GHz**: 20MHz
- **Channel Width 5GHz**: 80MHz
- **Channel Width 6GHz**: 80MHz (if available)
- **Transmit Power**: Auto
- **Band Steering**: ✅ Enabled
- **Fast Roaming**: ✅ Enabled
- **Multicast Enhancement**: ✅ Enabled
- **IGMP Snooping**: ✅ Enabled

#### UniFi AC Pro (General WiFi):
- **Location**: Main living areas
- **Networks**: HomeNetwork, HomeGuest, IoT-Trusted
- **Channel Width 2.4GHz**: 20MHz
- **Channel Width 5GHz**: 80MHz
- **Transmit Power**: Auto
- **Band Steering**: ✅ Enabled
- **Fast Roaming**: ✅ Enabled
- **Multicast Enhancement**: ✅ Enabled
- **IGMP Snooping**: ✅ Enabled

### 4. Switch Configuration (USW Flex 2.5G)

#### Port Profiles:
```json
{
  "port_1": {
    "name": "Synology NAS",
    "port_profile_id": "All",
    "poe_mode": "off"
  },
  "port_2": {
    "name": "UniFi AC Pro",
    "port_profile_id": "All",
    "poe_mode": "auto"
  },
  "port_3": {
    "name": "UniFi U6 Lite",
    "port_profile_id": "All", 
    "poe_mode": "auto"
  },
  "port_4": {
    "name": "Wired Clients",
    "port_profile_id": "LAN",
    "poe_mode": "off"
  },
  "port_5": {
    "name": "Future Expansion",
    "port_profile_id": "All",
    "poe_mode": "off"
  }
}
```

#### VLAN Configuration:
- **Native VLAN**: 1
- **Tagged VLANs**: 2, 3, 4, 6, 10, 11
- **Trunk Ports**: 2, 3, 5 (Access Points and uplinks)
- **Access Ports**: 1, 4 (NAS and wired clients)

### 5. Advanced Settings

#### Multicast and Broadcast Control:
- **Settings → System → Advanced**
- **Multicast Enhancement**: ✅ Enabled globally
- **IGMP Snooping**: ✅ Enabled globally
- **mDNS**: ✅ Enabled globally
- **Broadcast/Multicast Filtering**: ❌ Disabled (important for MusicCast)

#### Traffic Management:
- **Settings → Traffic Management**
- **Smart Queues**: ✅ Enabled
- **Bandwidth Profiles**:
  - Media Network: High priority for streaming
  - WiFi Users: Standard priority
  - Guest/IoT: Lower priority

### 6. Firewall & Security

#### Traffic Rules (Settings → Routing & Firewall → Traffic Rules):
```json
{
  "rules": [
    {
      "name": "Allow MusicCast Discovery",
      "enabled": true,
      "rule_index": 2000,
      "ruleset": "LAN_IN",
      "action": "accept",
      "protocol_match_excepted": false,
      "logging": false,
      "state_established": false,
      "state_invalid": false,
      "state_new": false,
      "state_related": false,
      "src_networkconf_id": "VLAN10_ID",
      "dst_networkconf_id": "VLAN3_ID",
      "dst_port": "5353,1900,5005,8080,49154"
    },
    {
      "name": "Allow Multicast to Media Network", 
      "enabled": true,
      "rule_index": 2001,
      "ruleset": "LAN_IN",
      "action": "accept",
      "protocol_match_excepted": false,
      "logging": false,
      "dst_address": "***************",
      "dst_port": "1900",
      "protocol": "udp"
    }
  ]
}
```

### 7. Quality of Service (QoS)

#### Traffic Categories:
1. **Media Streaming** (Highest Priority)
   - VLANs: 3
   - Ports: 80, 443, 5005, 8080, 30000-65535
   - Bandwidth: 40% guarantee

2. **Interactive Services** (High Priority)
   - VLANs: 1, 5, 10
   - Ports: 80, 443, 22, 51820
   - Bandwidth: 30% guarantee

3. **IoT Communication** (Medium Priority)
   - VLANs: 4
   - Bandwidth: 10% guarantee

4. **Guest/Untrusted** (Low Priority)
   - VLANs: 2, 6
   - Bandwidth: 20% guarantee

## Verification Steps

### 1. Check Network Status
- **Settings → System → Device Manager**
- Verify all APs are online and adopted
- Check VLAN assignments for each network

### 2. Monitor Client Connections
- **Clients → Active Clients**
- Verify MusicCast speakers appear on VLAN 3
- Check mobile devices on appropriate VLANs

### 3. Test Multicast Traffic
- **Settings → System → Logs**
- Look for IGMP join/leave messages
- Monitor mDNS query traffic

### 4. Wireless Performance
- **WiFi → WiFi Experience**
- Check signal strength and interference
- Verify channel utilization

## Troubleshooting Commands

### UniFi Controller CLI (if SSH enabled):
```bash
# Check VLAN configuration
info

# Monitor multicast traffic
tcpdump -i br3 port 5353 or port 1900

# Check IGMP snooping
brctl showstp br3

# Verify mDNS reflection
systemctl status avahi-daemon
```

### Common Issues and Solutions:

1. **Speakers not appearing in app**:
   - Verify VLAN 3 assignment
   - Check multicast enhancement enabled
   - Confirm firewall rules allow cross-VLAN traffic

2. **Discovery works but control fails**:
   - Check TCP port access (5005, 8080, 49154)
   - Verify bidirectional firewall rules
   - Test from same VLAN first

3. **Intermittent connectivity**:
   - Check wireless signal strength
   - Verify fast roaming configuration
   - Monitor for channel interference

## Next Steps After Configuration

1. **Apply all settings** and wait for devices to provision
2. **Restart MusicCast speakers** to refresh network settings
3. **Test discovery** from MusicCast app on different VLANs
4. **Monitor logs** for any blocked traffic or errors
5. **Fine-tune QoS** based on actual usage patterns
