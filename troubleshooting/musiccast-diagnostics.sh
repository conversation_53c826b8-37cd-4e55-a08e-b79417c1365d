#!/bin/bash
# MusicCast Network Diagnostics Script
# Comprehensive testing for Yamaha MusicCast cross-VLAN discovery issues

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MEDIA_NETWORK="***********/24"
MEDIA_GATEWAY="***********"
CORE_NETWORK="***********/24"
WIFI_NETWORK="************/24"
VPN_NETWORK="***********/24"

# MusicCast speaker IPs
SPEAKERS=(
    "***********0:livingroom"
    "***********1:kitchen"
    "***********2:bureau"
    "************:bedroom"
)

# MusicCast required ports
MUSICCAST_TCP_PORTS="80 443 5005 8080 49154"
MUSICCAST_UDP_PORTS="1900 5353 30000"

echo -e "${BLUE}=== Yamaha MusicCast Network Diagnostics ===${NC}"
echo "Testing MusicCast discovery and connectivity across VLANs"
echo "Date: $(date)"
echo ""

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} - $test_name"
    elif [ "$result" = "FAIL" ]; then
        echo -e "${RED}✗ FAIL${NC} - $test_name"
    elif [ "$result" = "WARN" ]; then
        echo -e "${YELLOW}⚠ WARN${NC} - $test_name"
    else
        echo -e "${BLUE}ℹ INFO${NC} - $test_name"
    fi
    
    if [ -n "$details" ]; then
        echo "  Details: $details"
    fi
    echo ""
}

# Function to test network connectivity
test_connectivity() {
    echo -e "${BLUE}=== Network Connectivity Tests ===${NC}"
    
    # Test gateway connectivity
    if ping -c 3 -W 2 "$MEDIA_GATEWAY" >/dev/null 2>&1; then
        print_result "Media Network Gateway" "PASS" "Gateway $MEDIA_GATEWAY is reachable"
    else
        print_result "Media Network Gateway" "FAIL" "Cannot reach gateway $MEDIA_GATEWAY"
    fi
    
    # Test speaker connectivity
    for speaker in "${SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        if ping -c 3 -W 2 "$ip" >/dev/null 2>&1; then
            print_result "Speaker Connectivity ($name)" "PASS" "$ip is reachable"
        else
            print_result "Speaker Connectivity ($name)" "FAIL" "$ip is not reachable"
        fi
    done
}

# Function to test DNS resolution
test_dns_resolution() {
    echo -e "${BLUE}=== DNS Resolution Tests ===${NC}"
    
    # Test local DNS resolution
    if nslookup google.com >/dev/null 2>&1; then
        print_result "External DNS Resolution" "PASS" "Can resolve external domains"
    else
        print_result "External DNS Resolution" "FAIL" "Cannot resolve external domains"
    fi
    
    # Test internal DNS (if configured)
    local_domains=(
        "livingroom.mdewaele.freeddns.org:***********0"
        "kitchen.mdewaele.freeddns.org:***********1"
        "bureau.mdewaele.freeddns.org:***********2"
        "bedroom.mdewaele.freeddns.org:************"
    )
    
    for domain_ip in "${local_domains[@]}"; do
        IFS=':' read -r domain expected_ip <<< "$domain_ip"
        resolved_ip=$(nslookup "$domain" 2>/dev/null | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        
        if [ "$resolved_ip" = "$expected_ip" ]; then
            print_result "DNS Resolution ($domain)" "PASS" "Resolves to $expected_ip"
        else
            print_result "DNS Resolution ($domain)" "FAIL" "Expected $expected_ip, got $resolved_ip"
        fi
    done
}

# Function to test multicast functionality
test_multicast() {
    echo -e "${BLUE}=== Multicast and mDNS Tests ===${NC}"
    
    # Check if multicast routing is enabled
    if [ -f /proc/sys/net/ipv4/ip_forward ] && [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
        print_result "IP Forwarding" "PASS" "IP forwarding is enabled"
    else
        print_result "IP Forwarding" "WARN" "IP forwarding may not be enabled"
    fi
    
    # Test multicast group membership
    if ip maddr show | grep -q "***************"; then
        print_result "SSDP Multicast Group" "PASS" "Device is member of SSDP multicast group"
    else
        print_result "SSDP Multicast Group" "WARN" "Device is not member of SSDP multicast group"
    fi
    
    # Test mDNS functionality
    if command -v avahi-browse >/dev/null 2>&1; then
        echo "Scanning for mDNS services (10 second timeout)..."
        services=$(timeout 10 avahi-browse -t _services._dns-sd._udp.local 2>/dev/null | wc -l)
        if [ "$services" -gt 0 ]; then
            print_result "mDNS Service Discovery" "PASS" "Found $services mDNS services"
        else
            print_result "mDNS Service Discovery" "WARN" "No mDNS services found"
        fi
    else
        print_result "mDNS Tools" "WARN" "avahi-browse not available for testing"
    fi
}

# Function to test MusicCast specific ports
test_musiccast_ports() {
    echo -e "${BLUE}=== MusicCast Port Accessibility Tests ===${NC}"
    
    for speaker in "${SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        echo "Testing ports for $name ($ip):"
        
        # Test TCP ports
        for port in $MUSICCAST_TCP_PORTS; do
            if timeout 3 bash -c "</dev/tcp/$ip/$port" 2>/dev/null; then
                print_result "TCP Port $port ($name)" "PASS" "Port is open and accessible"
            else
                print_result "TCP Port $port ($name)" "FAIL" "Port is not accessible"
            fi
        done
        
        # Test UDP ports (using nc if available)
        if command -v nc >/dev/null 2>&1; then
            for port in $MUSICCAST_UDP_PORTS; do
                if timeout 3 nc -u -z "$ip" "$port" 2>/dev/null; then
                    print_result "UDP Port $port ($name)" "PASS" "Port responds"
                else
                    print_result "UDP Port $port ($name)" "WARN" "Port may not be accessible (UDP test limitations)"
                fi
            done
        else
            print_result "UDP Port Testing" "WARN" "netcat not available for UDP port testing"
        fi
        echo ""
    done
}

# Function to test cross-VLAN communication
test_cross_vlan() {
    echo -e "${BLUE}=== Cross-VLAN Communication Tests ===${NC}"
    
    # Get current network interface and IP
    current_ip=$(ip route get ******* | grep -oP 'src \K\S+')
    current_network=""
    
    if [[ $current_ip =~ ^192\.168\.1\. ]]; then
        current_network="Core (VLAN 1)"
    elif [[ $current_ip =~ ^192\.168\.3\. ]]; then
        current_network="Media (VLAN 3)"
    elif [[ $current_ip =~ ^192\.168\.10\. ]]; then
        current_network="WiFi Users (VLAN 10)"
    elif [[ $current_ip =~ ^192\.168\.5\. ]]; then
        current_network="VPN (VLAN 5)"
    else
        current_network="Unknown"
    fi
    
    print_result "Current Network Location" "INFO" "$current_network ($current_ip)"
    
    # Test routing to different VLANs
    test_networks=(
        "***********:Core Gateway"
        "***********:Media Gateway"
        "************:WiFi Gateway"
        "***********:VPN Gateway"
    )
    
    for network in "${test_networks[@]}"; do
        IFS=':' read -r ip desc <<< "$network"
        if ping -c 2 -W 2 "$ip" >/dev/null 2>&1; then
            print_result "Route to $desc" "PASS" "$ip is reachable"
        else
            print_result "Route to $desc" "FAIL" "$ip is not reachable"
        fi
    done
}

# Function to capture network traffic for analysis
capture_traffic() {
    echo -e "${BLUE}=== Network Traffic Capture ===${NC}"
    
    if command -v tcpdump >/dev/null 2>&1; then
        echo "Capturing MusicCast-related traffic for 30 seconds..."
        echo "This will help identify discovery and communication patterns."
        echo ""
        
        # Create capture file with timestamp
        capture_file="/tmp/musiccast_capture_$(date +%Y%m%d_%H%M%S).pcap"
        
        # Capture multicast and MusicCast traffic
        timeout 30 tcpdump -i any -w "$capture_file" \
            '(multicast and (port 1900 or port 5353)) or (host ***********0 or host ***********1 or host ***********2 or host ************)' \
            >/dev/null 2>&1
        
        if [ -f "$capture_file" ]; then
            file_size=$(stat -f%z "$capture_file" 2>/dev/null || stat -c%s "$capture_file" 2>/dev/null)
            print_result "Traffic Capture" "PASS" "Captured to $capture_file ($file_size bytes)"
            
            # Basic analysis
            if command -v tcpdump >/dev/null 2>&1; then
                echo "Traffic summary:"
                tcpdump -r "$capture_file" -nn 2>/dev/null | head -10
                echo ""
            fi
        else
            print_result "Traffic Capture" "FAIL" "Could not create capture file"
        fi
    else
        print_result "Traffic Capture" "WARN" "tcpdump not available"
    fi
}

# Function to test MusicCast HTTP API
test_musiccast_api() {
    echo -e "${BLUE}=== MusicCast HTTP API Tests ===${NC}"
    
    for speaker in "${SPEAKERS[@]}"; do
        IFS=':' read -r ip name <<< "$speaker"
        
        # Test basic HTTP connectivity
        if command -v curl >/dev/null 2>&1; then
            # Test MusicCast API endpoint
            api_response=$(curl -s -m 5 "http://$ip/YamahaExtendedControl/v1/system/getDeviceInfo" 2>/dev/null)
            
            if echo "$api_response" | grep -q "response_code"; then
                print_result "MusicCast API ($name)" "PASS" "API responds correctly"
            else
                print_result "MusicCast API ($name)" "FAIL" "API not responding or incorrect response"
            fi
            
            # Test web interface
            web_response=$(curl -s -m 5 -I "http://$ip/" 2>/dev/null | head -1)
            if echo "$web_response" | grep -q "200\|302"; then
                print_result "Web Interface ($name)" "PASS" "Web interface accessible"
            else
                print_result "Web Interface ($name)" "FAIL" "Web interface not accessible"
            fi
        else
            print_result "HTTP API Testing" "WARN" "curl not available for API testing"
            break
        fi
    done
}

# Function to generate recommendations
generate_recommendations() {
    echo -e "${BLUE}=== Troubleshooting Recommendations ===${NC}"
    
    echo "Based on the test results, here are recommended actions:"
    echo ""
    
    echo "1. If speakers are not reachable:"
    echo "   - Verify speakers are connected to Media-6 WiFi (VLAN 3)"
    echo "   - Check DHCP reservations are correct"
    echo "   - Restart speakers to refresh network connection"
    echo ""
    
    echo "2. If multicast/mDNS tests fail:"
    echo "   - Apply the EdgeRouter mDNS configuration"
    echo "   - Enable IGMP snooping on UniFi networks"
    echo "   - Verify multicast enhancement is enabled"
    echo ""
    
    echo "3. If cross-VLAN communication fails:"
    echo "   - Apply the EdgeRouter firewall rules"
    echo "   - Check UniFi traffic rules allow cross-VLAN access"
    echo "   - Verify routing between VLANs is configured"
    echo ""
    
    echo "4. If MusicCast API tests fail:"
    echo "   - Check if speakers are in setup mode"
    echo "   - Verify firewall allows TCP ports 80, 5005, 8080"
    echo "   - Test from same VLAN first to isolate issues"
    echo ""
    
    echo "5. For ongoing monitoring:"
    echo "   - Use the traffic capture feature regularly"
    echo "   - Monitor EdgeRouter logs for blocked traffic"
    echo "   - Check UniFi controller for client connectivity issues"
    echo ""
}

# Main execution
main() {
    echo "Starting comprehensive MusicCast diagnostics..."
    echo "This may take several minutes to complete."
    echo ""
    
    test_connectivity
    test_dns_resolution
    test_multicast
    test_musiccast_ports
    test_cross_vlan
    test_musiccast_api
    capture_traffic
    generate_recommendations
    
    echo -e "${GREEN}=== Diagnostics Complete ===${NC}"
    echo "Review the results above and apply recommended configurations."
    echo "For detailed configuration steps, see:"
    echo "- musiccast-edgerouter-config.sh"
    echo "- musiccast-unifi-config.md"
    echo ""
}

# Check if running as root for some network tests
if [ "$EUID" -ne 0 ]; then
    echo -e "${YELLOW}Note: Some tests may require root privileges for full functionality${NC}"
    echo ""
fi

# Run main function
main
