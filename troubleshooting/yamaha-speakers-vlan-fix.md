# Yamaha Speakers VLAN Configuration Fix

## Problem
Wired Yamaha speakers lost connectivity after changing IPs to 192.168.3.x because they're connected through an unmanaged 24-port TP-Link switch that cannot handle VLAN tagging.

## Current Network Path
```
EdgeRouter 4 → USW Flex 2.5G → 24-Port TP-Link (Unmanaged) → Yamaha Speakers
```

## Root Cause
- **Unmanaged Switch**: Cannot process VLAN tags
- **Port Configuration**: USW Flex port 4 is configured as VLAN 1 access port
- **IP Mismatch**: Speakers configured for VLAN 3 but receiving untagged VLAN 1 traffic

## Solution Options

### **Option 1: Keep Speakers on VLAN 1 (Recommended)**

This is the simplest solution that works with your current hardware.

#### 1.1 Change Speaker IPs Back to VLAN 1
```bash
# SSH to EdgeRouter and update DHCP reservations
configure

# Remove VLAN 3 reservations
delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker
delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker
delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker
delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker

# Add VLAN 1 reservations for speakers
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping livingroom-speaker ip-address ************
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping livingroom-speaker mac-address XX:XX:XX:XX:XX:XX
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping kitchen-speaker ip-address ************
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping kitchen-speaker mac-address XX:XX:XX:XX:XX:XX
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bureau-speaker ip-address ************
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bureau-speaker mac-address XX:XX:XX:XX:XX:XX
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bedroom-speaker ip-address ************
set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bedroom-speaker mac-address XX:XX:XX:XX:XX:XX

commit
save
exit
```

#### 1.2 Update Firewall Rules for Cross-VLAN Access
```bash
# Allow WiFi users (VLAN 10) to access wired speakers (VLAN 1)
configure

set firewall name WIFI_USERS_IN rule 20 action accept
set firewall name WIFI_USERS_IN rule 20 description 'Allow access to Yamaha speakers'
set firewall name WIFI_USERS_IN rule 20 destination address ************-************
set firewall name WIFI_USERS_IN rule 20 destination port 80,443,1900,5005,5353,8080,49154
set firewall name WIFI_USERS_IN rule 20 protocol tcp_udp

# Allow Media WiFi (VLAN 3) to access wired speakers (VLAN 1)
set firewall name MEDIA_IN rule 20 action accept
set firewall name MEDIA_IN rule 20 description 'Allow Media WiFi to access wired speakers'
set firewall name MEDIA_IN rule 20 destination address ************-************
set firewall name MEDIA_IN rule 20 destination port 80,443,1900,5005,5353,8080,49154
set firewall name MEDIA_IN rule 20 protocol tcp_udp

commit
save
exit
```

#### 1.3 Reset Speakers to Get New IPs
1. Factory reset each Yamaha speaker
2. They should automatically get the new VLAN 1 IP addresses
3. Test connectivity from different VLANs

### **Option 2: Configure USW Flex for VLAN 3 Access**

This requires changing the USW Flex port configuration.

#### 2.1 Change USW Flex Port 4 to VLAN 3 Access
In UniFi Controller:
1. Go to **Devices** → **USW Flex 2.5G**
2. Click **Ports** tab
3. Edit **Port 4** (connected to 24-port switch)
4. Change from **LAN** profile to **Media Network** (VLAN 3)
5. Set as **Access** port (not trunk)
6. Apply changes

#### 2.2 Update DHCP Reservations
Keep the existing VLAN 3 reservations (************-13) but ensure MAC addresses are correct.

#### 2.3 Considerations
- **All devices** on the 24-port switch will be on VLAN 3
- May affect other wired devices connected to that switch
- Need to plan which devices should be on VLAN 3

### **Option 3: Use Managed Switch (Future Upgrade)**

For maximum flexibility, replace the 24-port unmanaged switch with a managed switch.

#### Recommended Switches:
- **TP-Link TL-SG3428X**: 24-port managed with 4x 10G SFP+
- **UniFi Switch 24**: Native UniFi integration
- **Netgear GS728TP**: 24-port PoE+ managed

#### Benefits:
- Per-port VLAN assignment
- Advanced QoS and monitoring
- Better integration with UniFi ecosystem

## **Immediate Action Plan**

### **Step 1: Verify Current Status**
```bash
# Check current speaker connectivity
ping ************
ping ************
ping ************
ping ************

# Check DHCP leases
show dhcp leases | grep -E "(192.168.1|192.168.3)"
```

### **Step 2: Choose Solution**
I recommend **Option 1** (move back to VLAN 1) because:
- ✅ Works with existing hardware
- ✅ Minimal configuration changes
- ✅ Maintains network stability
- ✅ Allows cross-VLAN discovery with proper firewall rules

### **Step 3: Implement Solution**
1. Apply the DHCP reservation changes above
2. Factory reset speakers to get new IPs
3. Update firewall rules for cross-VLAN access
4. Test MusicCast discovery from different VLANs

### **Step 4: Test Cross-VLAN Discovery**
After implementing Option 1:
1. Speakers will be on VLAN 1 (************-13)
2. MusicCast app on VLAN 10 (HomeNetwork WiFi) should discover them
3. MusicCast app on VLAN 3 (Media-6 WiFi) should also discover them
4. Use the troubleshooting scripts to verify connectivity

## **Network Diagram After Fix**

```
EdgeRouter 4 (***********)
    │
USW Flex 2.5G (***********)
    ├── Port 1: Synology NAS (************3)
    ├── Port 2: UniFi AC Pro (trunk all VLANs)
    ├── Port 3: UniFi U6 Lite (trunk all VLANs)
    ├── Port 4: 24-Port Switch (VLAN 1 access) ← SPEAKERS HERE
    └── Port 5: Future expansion

24-Port TP-Link Switch (Unmanaged)
    ├── Yamaha Living Room (************)
    ├── Yamaha Kitchen (************)
    ├── Yamaha Bureau (************)
    ├── Yamaha Bedroom (************)
    └── Other wired devices (192.168.1.x)
```

## **Why This Happens**

**VLAN Tagging Basics:**
- **Managed switches**: Can add/remove VLAN tags
- **Unmanaged switches**: Pass traffic as-is, no VLAN processing
- **Access ports**: Remove VLAN tags before sending to devices
- **Trunk ports**: Keep VLAN tags for other switches/APs

**Your Situation:**
- USW Flex port 4 is configured as VLAN 1 access port
- It removes all VLAN tags before sending to 24-port switch
- 24-port switch passes untagged traffic to speakers
- Speakers configured for VLAN 3 can't communicate with VLAN 1 gateway

## **Next Steps**

1. **Immediate**: Implement Option 1 to restore connectivity
2. **Short-term**: Test cross-VLAN MusicCast discovery
3. **Long-term**: Consider managed switch upgrade for more flexibility

Would you like me to help you implement Option 1 step by step?
