# Yamaha MusicCast VLAN 3 Discovery Troubleshooting Guide

## Problem Description
Yamaha MusicCast speakers connected to MDW-Media WiFi (VLAN 3) are not discoverable by the MusicCast Android app when the mobile device is on different network segments.

## Network Configuration Summary
- **VLAN 3 (Media Network)**: ***********/24
- **WiFi SSID**: "Media-6" (UniFi U6 Lite)
- **Speaker IPs**: ***********0-13 (static reservations)
- **Gateway**: *********** (EdgeRouter 4)

## Root Cause Analysis

### Primary Issues Identified:
1. **Cross-VLAN mDNS Discovery**: MusicCast uses mDNS (Bonjour) for device discovery
2. **Multicast Traffic Isolation**: VLAN segmentation blocks multicast between networks
3. **Firewall Restrictions**: Current media network firewall may block required ports
4. **Missing MusicCast-Specific Ports**: Several UDP/TCP ports need to be opened

## Phase 1: Network Configuration Verification

### 1.1 Verify Current VLAN 3 Configuration
```bash
# SSH to EdgeRouter and check VLAN 3 interface
show interfaces ethernet eth1 vif 3
show service dhcp-server shared-network-name MEDIA

# Verify multicast configuration
show protocols igmp-proxy
show service mdns repeater
```

### 1.2 Check UniFi Controller Settings
- Verify "Media-6" SSID is assigned to VLAN 3
- Confirm multicast enhancement is enabled
- Check if IGMP snooping is properly configured

### 1.3 Verify Speaker Network Connectivity
```bash
# Test basic connectivity to speakers
ping ***********0  # Living room
ping ***********1  # Kitchen
ping ***********2  # Bureau
ping ***********3  # Bedroom

# Check DHCP leases
show dhcp leases
```

## Phase 2: MusicCast Protocol Requirements

### 2.1 Required Ports for Yamaha MusicCast
Based on Yamaha documentation and network analysis:

**TCP Ports:**
- 80: HTTP communication
- 443: HTTPS communication  
- 5005: MusicCast control protocol
- 8080: Web interface access
- 49154: UPnP control

**UDP Ports:**
- 1900: SSDP (Simple Service Discovery Protocol)
- 5353: mDNS (Multicast DNS)
- 30000-65535: Dynamic port range for streaming
- ***************: SSDP multicast address

### 2.2 Multicast Addresses Used by MusicCast
- ***************:1900 (SSDP discovery)
- ***********:5353 (mDNS)
- Various streaming multicast addresses

## Phase 3: Cross-VLAN Communication Setup

### 3.1 Enhanced mDNS Configuration
Current configuration needs expansion to support cross-VLAN discovery.

### 3.2 IGMP Proxy Enhancement
Need to ensure proper multicast routing between VLANs where MusicCast app might be running.

### 3.3 Firewall Rules for Cross-VLAN Access
Allow specific MusicCast traffic between VLANs while maintaining security.

## Phase 4: Firewall Rules Optimization

### 4.1 Current Media Network Firewall Analysis
The current MEDIA_IN firewall rules are restrictive and may block MusicCast discovery.

### 4.2 Required Firewall Modifications
Need to add rules for:
- mDNS traffic (UDP 5353)
- SSDP traffic (UDP 1900)
- MusicCast control ports
- Cross-VLAN communication for discovery

## Phase 5: Network Diagnostics & Testing

### 5.1 Multicast Testing Tools
- Use `tcpdump` to monitor multicast traffic
- Test mDNS queries across VLANs
- Verify IGMP membership

### 5.2 MusicCast App Testing Scenarios
1. App on same VLAN 3 (Media-6 WiFi)
2. App on VLAN 10 (HomeNetwork WiFi)
3. App on VLAN 1 (wired network)
4. App via VPN (VLAN 5)

## Implementation Priority

### High Priority (Must Fix)
1. Configure MusicCast-specific firewall rules
2. Enable cross-VLAN mDNS reflection
3. Open required UDP/TCP ports

### Medium Priority (Should Fix)
1. Optimize IGMP proxy configuration
2. Add network monitoring for troubleshooting
3. Create automated testing scripts

### Low Priority (Nice to Have)
1. QoS optimization for streaming
2. Advanced multicast routing
3. Network performance monitoring

## Next Steps
1. Apply enhanced firewall configuration
2. Update mDNS and IGMP settings
3. Test discovery from different network segments
4. Monitor network traffic during discovery attempts
5. Iterate based on test results

---
*This troubleshooting guide addresses the specific network topology with EdgeRouter 4, UniFi infrastructure, and VLAN segmentation.*
