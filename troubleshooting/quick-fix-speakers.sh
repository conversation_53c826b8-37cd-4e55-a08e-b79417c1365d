#!/bin/bash
# Quick Fix for Yamaha Speakers VLAN Issue
# This script moves the speakers back to VLAN 1 and configures cross-VLAN access

echo "=== Yamaha Speakers VLAN Quick Fix ==="
echo "Moving speakers back to VLAN 1 to restore connectivity"
echo "Date: $(date)"
echo ""

echo "IMPORTANT: Run these commands on your EdgeRouter 4 via SSH"
echo ""

echo "Problem: Wired speakers on unmanaged switch cannot use VLAN 3 IPs"
echo "Solution: Move speakers to VLAN 1 with cross-VLAN discovery support"
echo ""

echo "1. Remove VLAN 3 DHCP reservations for speakers:"
echo "configure"
echo ""

echo "delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping livingroom-speaker"
echo "delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping kitchen-speaker"
echo "delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bureau-speaker"
echo "delete service dhcp-server shared-network-name MEDIA subnet ***********/24 static-mapping bedroom-speaker"
echo ""

echo "2. Add VLAN 1 DHCP reservations for speakers:"
echo "# NOTE: Replace XX:XX:XX:XX:XX:XX with actual MAC addresses of your speakers"
echo ""

echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping livingroom-speaker ip-address ***********0"
echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping livingroom-speaker mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping kitchen-speaker ip-address ***********1"
echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping kitchen-speaker mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bureau-speaker ip-address ***********2"
echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bureau-speaker mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bedroom-speaker ip-address ***********3"
echo "set service dhcp-server shared-network-name LAN subnet ***********/24 static-mapping bedroom-speaker mac-address XX:XX:XX:XX:XX:XX"
echo ""

echo "3. Create network group for speakers:"
echo "set firewall group network-group YAMAHA_SPEAKERS network ***********0/32"
echo "set firewall group network-group YAMAHA_SPEAKERS network ***********1/32"
echo "set firewall group network-group YAMAHA_SPEAKERS network ***********2/32"
echo "set firewall group network-group YAMAHA_SPEAKERS network ***********3/32"
echo "set firewall group network-group YAMAHA_SPEAKERS description 'Yamaha MusicCast Speakers on VLAN 1'"
echo ""

echo "4. Create port group for MusicCast ports:"
echo "set firewall group port-group MUSICCAST_PORTS port 80"
echo "set firewall group port-group MUSICCAST_PORTS port 443"
echo "set firewall group port-group MUSICCAST_PORTS port 1900"
echo "set firewall group port-group MUSICCAST_PORTS port 5005"
echo "set firewall group port-group MUSICCAST_PORTS port 5353"
echo "set firewall group port-group MUSICCAST_PORTS port 8080"
echo "set firewall group port-group MUSICCAST_PORTS port 49154"
echo "set firewall group port-group MUSICCAST_PORTS description 'MusicCast required ports'"
echo ""

echo "5. Allow WiFi Users (VLAN 10) to access speakers:"
echo "set firewall name WIFI_USERS_IN rule 20 action accept"
echo "set firewall name WIFI_USERS_IN rule 20 description 'Allow WiFi users to access Yamaha speakers'"
echo "set firewall name WIFI_USERS_IN rule 20 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name WIFI_USERS_IN rule 20 destination group port-group MUSICCAST_PORTS"
echo "set firewall name WIFI_USERS_IN rule 20 protocol tcp_udp"
echo ""

echo "6. Allow Media WiFi (VLAN 3) to access speakers:"
echo "set firewall name MEDIA_IN rule 25 action accept"
echo "set firewall name MEDIA_IN rule 25 description 'Allow Media WiFi to access wired speakers'"
echo "set firewall name MEDIA_IN rule 25 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name MEDIA_IN rule 25 destination group port-group MUSICCAST_PORTS"
echo "set firewall name MEDIA_IN rule 25 protocol tcp_udp"
echo ""

echo "7. Allow multicast discovery from WiFi networks to speakers:"
echo "set firewall name WIFI_USERS_IN rule 21 action accept"
echo "set firewall name WIFI_USERS_IN rule 21 description 'Allow mDNS discovery to speakers'"
echo "set firewall name WIFI_USERS_IN rule 21 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name WIFI_USERS_IN rule 21 destination port 5353"
echo "set firewall name WIFI_USERS_IN rule 21 protocol udp"
echo ""

echo "set firewall name WIFI_USERS_IN rule 22 action accept"
echo "set firewall name WIFI_USERS_IN rule 22 description 'Allow SSDP discovery to speakers'"
echo "set firewall name WIFI_USERS_IN rule 22 destination group network-group YAMAHA_SPEAKERS"
echo "set firewall name WIFI_USERS_IN rule 22 destination port 1900"
echo "set firewall name WIFI_USERS_IN rule 22 protocol udp"
echo ""

echo "8. Enhanced mDNS repeater for cross-VLAN discovery:"
echo "delete service mdns repeater"
echo "set service mdns repeater interface eth1"        # Core network (VLAN 1)
echo "set service mdns repeater interface eth1.3"      # Media network (VLAN 3)
echo "set service mdns repeater interface eth1.10"     # WiFi users (VLAN 10)
echo "set service mdns repeater interface wg0"         # VPN network (VLAN 5)"
echo ""

echo "9. Commit and save configuration:"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== After Configuration ==="
echo ""
echo "Physical Steps Required:"
echo "1. Factory reset each Yamaha speaker (hold setup button for 10+ seconds)"
echo "2. Wait for speakers to boot up and get new DHCP leases"
echo "3. Verify new IP addresses:"
echo "   - Living Room: ***********0"
echo "   - Kitchen: ***********1"
echo "   - Bureau: ***********2"
echo "   - Bedroom: ***********3"
echo ""

echo "Testing Steps:"
echo "1. Test basic connectivity:"
echo "   ping ***********0"
echo "   ping ***********1"
echo "   ping ***********2"
echo "   ping ***********3"
echo ""

echo "2. Test MusicCast discovery:"
echo "   - Connect phone to HomeNetwork WiFi (VLAN 10)"
echo "   - Open MusicCast app and scan for devices"
echo "   - Connect phone to Media-6 WiFi (VLAN 3)"
echo "   - Test discovery again"
echo ""

echo "3. Monitor traffic (optional):"
echo "   sudo tcpdump -i eth1 host ***********0 or host ***********1"
echo ""

echo "Troubleshooting Commands:"
echo "- show dhcp leases | grep -E '***********[0-3]'"
echo "- show firewall name WIFI_USERS_IN"
echo "- show service mdns repeater"
echo "- show log tail 50 | grep -i musiccast"
echo ""

echo "=== Why This Solution Works ==="
echo ""
echo "Network Path:"
echo "EdgeRouter → USW Flex (Port 4, VLAN 1 access) → 24-Port Unmanaged → Speakers"
echo ""
echo "Discovery Path:"
echo "Mobile App (VLAN 10/3) → EdgeRouter → mDNS Repeater → Speakers (VLAN 1)"
echo ""
echo "Key Components:"
echo "- Speakers on VLAN 1: Compatible with unmanaged switch"
echo "- Cross-VLAN firewall rules: Allow discovery and control"
echo "- mDNS repeater: Enables service discovery across VLANs"
echo "- Static IP reservations: Predictable addressing"
echo ""

echo "=== Important Notes ==="
echo ""
echo "1. MAC Addresses: You MUST replace XX:XX:XX:XX:XX:XX with actual MAC addresses"
echo "   - Find MAC addresses on speaker labels or in current DHCP leases"
echo "   - Use: show dhcp leases | grep -i yamaha"
echo ""
echo "2. Network Topology: This assumes your current physical connections:"
echo "   - EdgeRouter eth1 → USW Flex uplink"
echo "   - USW Flex port 4 → 24-port TP-Link switch"
echo "   - Speakers connected to 24-port switch"
echo ""
echo "3. Alternative: If you want speakers on VLAN 3, you need to:"
echo "   - Change USW Flex port 4 to VLAN 3 access mode"
echo "   - This affects ALL devices on the 24-port switch"
echo ""

echo "Need help finding MAC addresses? Run:"
echo "show dhcp leases"
echo "show arp"
