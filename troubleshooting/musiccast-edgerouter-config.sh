#!/bin/bash
# EdgeRouter 4 Configuration for Yamaha MusicCast Cross-VLAN Discovery
# This script configures the EdgeRouter to support MusicCast device discovery
# across VLANs while maintaining network security

echo "=== Yamaha MusicCast Cross-VLAN Discovery Configuration ==="
echo "Configuring EdgeRouter 4 for MusicCast speakers on VLAN 3"
echo "Date: $(date)"
echo ""

echo "IMPORTANT: Run these commands on your EdgeRouter 4 via SSH"
echo "Network Configuration:"
echo "- VLAN 3 (Media): ***********/24 - MusicCast speakers"
echo "- VLAN 1 (Core): ***********/24 - Wired devices"
echo "- VLAN 10 (WiFi): ************/24 - Primary WiFi users"
echo "- VLAN 5 (VPN): ***********/24 - VPN clients"
echo ""

echo "1. Enhanced mDNS Repeater Configuration:"
echo "configure"
echo ""

# Enhanced mDNS configuration for cross-VLAN discovery
echo "# Remove existing mDNS configuration"
echo "delete service mdns repeater"
echo ""

echo "# Configure enhanced mDNS repeater for MusicCast discovery"
echo "set service mdns repeater interface eth1"        # Core network (VLAN 1)
echo "set service mdns repeater interface eth1.3"      # Media network (VLAN 3)
echo "set service mdns repeater interface eth1.10"     # WiFi users (VLAN 10)
echo "set service mdns repeater interface wg0"         # VPN network (VLAN 5)"
echo ""

echo "# Enable mDNS reflection between specific VLANs"
echo "set service mdns repeater browse-domain local"
echo "set service mdns repeater browse-domain _musiccast._tcp.local"
echo "set service mdns repeater browse-domain _yamaha-mc._tcp.local"
echo ""

echo "2. Enhanced IGMP Proxy for Multicast Streaming:"
echo ""

echo "# Remove existing IGMP proxy configuration"
echo "delete protocols igmp-proxy"
echo ""

echo "# Configure IGMP proxy for multicast support"
echo "set protocols igmp-proxy interface eth1 role upstream"
echo "set protocols igmp-proxy interface eth1 alt-subnet ***********/24"
echo "set protocols igmp-proxy interface eth1.3 role downstream"
echo "set protocols igmp-proxy interface eth1.3 alt-subnet ***********/24"
echo "set protocols igmp-proxy interface eth1.10 role downstream"
echo "set protocols igmp-proxy interface eth1.10 alt-subnet ************/24"
echo "set protocols igmp-proxy interface wg0 role downstream"
echo "set protocols igmp-proxy interface wg0 alt-subnet ***********/24"
echo ""

echo "3. MusicCast-Specific Firewall Rules:"
echo ""

echo "# Create port group for MusicCast ports"
echo "set firewall group port-group MUSICCAST_PORTS port 80"
echo "set firewall group port-group MUSICCAST_PORTS port 443"
echo "set firewall group port-group MUSICCAST_PORTS port 1900"
echo "set firewall group port-group MUSICCAST_PORTS port 5005"
echo "set firewall group port-group MUSICCAST_PORTS port 5353"
echo "set firewall group port-group MUSICCAST_PORTS port 8080"
echo "set firewall group port-group MUSICCAST_PORTS port 49154"
echo "set firewall group port-group MUSICCAST_PORTS description 'Yamaha MusicCast required ports'"
echo ""

echo "# Create network group for MusicCast speakers"
echo "set firewall group network-group MUSICCAST_SPEAKERS network ************/32"
echo "set firewall group network-group MUSICCAST_SPEAKERS network ************/32"
echo "set firewall group network-group MUSICCAST_SPEAKERS network ************/32"
echo "set firewall group network-group MUSICCAST_SPEAKERS network ************/32"
echo "set firewall group network-group MUSICCAST_SPEAKERS description 'Yamaha MusicCast speaker IPs'"
echo ""

echo "# Create network group for client networks that need MusicCast access"
echo "set firewall group network-group MUSICCAST_CLIENTS network ***********/24"   # Core/wired
echo "set firewall group network-group MUSICCAST_CLIENTS network ************/24"  # WiFi users
echo "set firewall group network-group MUSICCAST_CLIENTS network ***********/24"   # VPN
echo "set firewall group network-group MUSICCAST_CLIENTS description 'Networks allowed to access MusicCast'"
echo ""

echo "4. Update Media Network Firewall Rules:"
echo ""

echo "# Backup existing media firewall rules"
echo "show firewall name MEDIA_IN | save /tmp/media_firewall_backup.txt"
echo ""

echo "# Add MusicCast discovery rules to MEDIA_IN"
echo "set firewall name MEDIA_IN rule 5 action accept"
echo "set firewall name MEDIA_IN rule 5 description 'Allow mDNS discovery from client networks'"
echo "set firewall name MEDIA_IN rule 5 source group network-group MUSICCAST_CLIENTS"
echo "set firewall name MEDIA_IN rule 5 destination group network-group MUSICCAST_SPEAKERS"
echo "set firewall name MEDIA_IN rule 5 destination port 5353"
echo "set firewall name MEDIA_IN rule 5 protocol udp"
echo ""

echo "set firewall name MEDIA_IN rule 6 action accept"
echo "set firewall name MEDIA_IN rule 6 description 'Allow SSDP discovery from client networks'"
echo "set firewall name MEDIA_IN rule 6 source group network-group MUSICCAST_CLIENTS"
echo "set firewall name MEDIA_IN rule 6 destination address ***************"
echo "set firewall name MEDIA_IN rule 6 destination port 1900"
echo "set firewall name MEDIA_IN rule 6 protocol udp"
echo ""

echo "set firewall name MEDIA_IN rule 7 action accept"
echo "set firewall name MEDIA_IN rule 7 description 'Allow MusicCast control from client networks'"
echo "set firewall name MEDIA_IN rule 7 source group network-group MUSICCAST_CLIENTS"
echo "set firewall name MEDIA_IN rule 7 destination group network-group MUSICCAST_SPEAKERS"
echo "set firewall name MEDIA_IN rule 7 destination group port-group MUSICCAST_PORTS"
echo "set firewall name MEDIA_IN rule 7 protocol tcp"
echo ""

echo "set firewall name MEDIA_IN rule 8 action accept"
echo "set firewall name MEDIA_IN rule 8 description 'Allow MusicCast streaming from client networks'"
echo "set firewall name MEDIA_IN rule 8 source group network-group MUSICCAST_CLIENTS"
echo "set firewall name MEDIA_IN rule 8 destination group network-group MUSICCAST_SPEAKERS"
echo "set firewall name MEDIA_IN rule 8 destination port 30000-65535"
echo "set firewall name MEDIA_IN rule 8 protocol udp"
echo ""

echo "5. Create Reverse Rules for Client Networks:"
echo ""

echo "# Allow MusicCast speakers to respond to client networks"
echo "# This is needed for bidirectional communication"

echo "# Update WIFI_USERS_IN firewall (VLAN 10)"
echo "set firewall name WIFI_USERS_IN rule 15 action accept"
echo "set firewall name WIFI_USERS_IN rule 15 description 'Allow access to MusicCast speakers'"
echo "set firewall name WIFI_USERS_IN rule 15 destination group network-group MUSICCAST_SPEAKERS"
echo "set firewall name WIFI_USERS_IN rule 15 destination group port-group MUSICCAST_PORTS"
echo "set firewall name WIFI_USERS_IN rule 15 protocol tcp_udp"
echo ""

echo "# Allow multicast traffic from WiFi users"
echo "set firewall name WIFI_USERS_IN rule 16 action accept"
echo "set firewall name WIFI_USERS_IN rule 16 description 'Allow multicast discovery to media network'"
echo "set firewall name WIFI_USERS_IN rule 16 destination address ***************"
echo "set firewall name WIFI_USERS_IN rule 16 destination port 1900"
echo "set firewall name WIFI_USERS_IN rule 16 protocol udp"
echo ""

echo "6. Commit and Save Configuration:"
echo ""
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Configuration Complete ==="
echo ""
echo "Next Steps:"
echo "1. Verify configuration: show firewall name MEDIA_IN"
echo "2. Check mDNS: show service mdns repeater"
echo "3. Verify IGMP: show protocols igmp-proxy"
echo "4. Test MusicCast discovery from different VLANs"
echo "5. Monitor traffic: sudo tcpdump -i eth1.3 port 5353 or port 1900"
echo ""
echo "Troubleshooting Commands:"
echo "- show log tail 50 | grep -i musiccast"
echo "- show firewall statistics"
echo "- show protocols igmp-proxy interface"
echo "- show service mdns repeater statistics"
