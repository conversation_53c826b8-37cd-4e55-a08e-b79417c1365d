# Traefik Dynamic Configuration
# Middleware and additional routing rules
#
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: ***********13)
# - WiFi LAN: ***********/24
# - Guest Network: **********/24 (BLOCKED - Internet only, no internal access)
# - VPN Network: ***********/24
#
# Port Configuration:
# - Traefik HTTP: 8080 (to avoid Synology NAS port 80 conflict)
# - Traefik HTTPS: 8443 (to avoid Synology NAS port 443 conflict)

# Middleware definitions
http:
  middlewares:
    # IP whitelist middleware - only allow internal networks (EXCLUDES guest network)
    internal-only:
      ipWhiteList:
        sourceRange:
          - "***********/24"    # Wired LAN
          - "***********/24"    # WiFi LAN
          - "***********/24"    # VPN network
          - "127.0.0.1/32"      # Localhost
          - "::1/128"           # IPv6 localhost
          # NOTE: **********/24 (guest network) is INTENTIONALLY EXCLUDED
    
    # Security headers
    security-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
        accessControlMaxAge: 100
        hostsProxyHeaders:
          - "X-Forwarded-Host"
        referrerPolicy: "same-origin"
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          X-Robots-Tag: "none,noarchive,nosnippet,notranslate,noimageindex"
          server: ""
        sslProxyHeaders:
          X-Forwarded-Proto: https
    
    # Rate limiting
    rate-limit:
      rateLimit:
        average: 100
        burst: 50
    
    # Redirect HTTP to HTTPS
    https-redirect:
      redirectScheme:
        scheme: https
        permanent: true

  # Router definitions for services
  routers:
    # Traefik Dashboard
    traefik-dashboard:
      rule: "Host(`traefik.mdewaele.freeddns.org`)"
      service: api@internal
      middlewares:
        - internal-only
        - security-headers
      tls:
        certResolver: letsencrypt
    
    # Example service configurations
    # Uncomment and modify as needed for your services
    
    # Home service
    # home-service:
    #   rule: "Host(`home.mdewaele.freeddns.org`)"
    #   service: home-service
    #   middlewares:
    #     - internal-only
    #     - security-headers
    #     - https-redirect
    #   tls:
    #     certResolver: letsencrypt
    
    # AdGuard Home
    # adguard-service:
    #   rule: "Host(`adguard.mdewaele.freeddns.org`)"
    #   service: adguard-service
    #   middlewares:
    #     - internal-only
    #     - security-headers
    #     - https-redirect
    #   tls:
    #     certResolver: letsencrypt
    
    # Router management
    # router-service:
    #   rule: "Host(`router.mdewaele.freeddns.org`)"
    #   service: router-service
    #   middlewares:
    #     - internal-only
    #     - security-headers
    #     - https-redirect
    #   tls:
    #     certResolver: letsencrypt

  # Service definitions
  services:
    # Example service definitions
    # Uncomment and modify as needed
    
    # home-service:
    #   loadBalancer:
    #     servers:
    #       - url: "http://192.168.1.XXX:PORT"  # Replace with actual IP and port
    
    # adguard-service:
    #   loadBalancer:
    #     servers:
    #       - url: "http://192.168.1.XXX:3000"  # Replace with AdGuard IP
    
    # router-service:
    #   loadBalancer:
    #     servers:
    #       - url: "https://***********"  # EdgeRouter IP

# TLS configuration
tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
