# Synology NAS Port Conflict Resolution

## Problem
The Synology NAS at ************* has built-in web services running on ports 80 and 443 that cannot be disabled. This creates a conflict when DNS rewrites point subdomains to *************, as requests may hit the NAS web interface instead of Traefik.

## Solution Overview
Configure Traefik to use alternative ports (8080/8443) and update DNS rewrites to include port numbers.

## Implementation

### 1. Traefik Port Configuration
- **HTTP**: Port 8080 (instead of 80)
- **HTTPS**: Port 8443 (instead of 443)
- **Dashboard**: Port 9080 (alternative access)

### 2. DNS Rewrite Configuration
Update AdGuard Home DNS rewrites to include port numbers:

```
# Standard format (will hit Synology NAS web interface)
home.mdewaele.freeddns.org → *************

# Corrected format with ports (will hit <PERSON>raefik)
home.mdewaele.freeddns.org → *************:8080 (HTTP)
home.mdewaele.freeddns.org → *************:8443 (HTTPS)
```

### 3. Alternative Solutions

#### Option A: Use Port-Specific DNS Rewrites
Create separate DNS entries for HTTP and HTTPS:
```
home-http.mdewaele.freeddns.org → *************:8080
home-https.mdewaele.freeddns.org → *************:8443
```

#### Option B: Use Nginx Proxy on NAS
Configure Nginx on the Synology NAS to proxy requests:
1. Install Nginx on Synology
2. Configure Nginx to listen on ports 80/443
3. Proxy requests to Traefik on ports 8080/8443

#### Option C: Use Different IP Address
Assign a secondary IP to the Synology NAS:
1. Add alias IP: `ip addr add *************/24 dev eth0`
2. Bind Traefik to *************:80/443
3. Update DNS rewrites to point to *************

## Recommended Implementation

### Step 1: Update Traefik Configuration
Use the provided `traefik.yml`, `dynamic.yml`, and `docker-compose.yml` files which are already configured for ports 8080/8443.

### Step 2: Configure AdGuard Home DNS Rewrites
Since AdGuard Home doesn't support port numbers in DNS rewrites, use one of these approaches:

#### Approach 1: Client-Side Configuration
Update client configurations to use specific ports:
```
# In browser or applications
http://home.mdewaele.freeddns.org:8080
https://home.mdewaele.freeddns.org:8443
```

#### Approach 2: Nginx Reverse Proxy (Recommended)
Install and configure Nginx on Synology NAS:

```nginx
# /etc/nginx/sites-available/traefik-proxy
server {
    listen 80;
    server_name home.mdewaele.freeddns.org traefik.mdewaele.freeddns.org adguard.mdewaele.freeddns.org;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 443 ssl;
    server_name home.mdewaele.freeddns.org traefik.mdewaele.freeddns.org adguard.mdewaele.freeddns.org;
    
    # SSL configuration (use Let's Encrypt or existing certificates)
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass https://127.0.0.1:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_ssl_verify off;
    }
}
```

### Step 3: Firewall Configuration
Update EdgeRouter firewall rules to allow the new ports:

```bash
configure
set firewall name WAN_IN rule 21 action drop
set firewall name WAN_IN rule 21 description 'Block Traefik ports from WAN'
set firewall name WAN_IN rule 21 destination port 8080,8443,9080
set firewall name WAN_IN rule 21 protocol tcp
set firewall name WAN_IN rule 21 log enable
commit
save
exit
```

## Testing the Configuration

### 1. Test Direct Port Access
```bash
# Should work when connected via VPN or local network
curl http://*************:8080
curl https://*************:8443
```

### 2. Test Subdomain Access
```bash
# If using Nginx proxy approach
curl http://home.mdewaele.freeddns.org
curl https://home.mdewaele.freeddns.org

# If using direct port approach
curl http://home.mdewaele.freeddns.org:8080
curl https://home.mdewaele.freeddns.org:8443
```

### 3. Verify No External Access
```bash
# From external network - should fail
curl http://YOUR_EXTERNAL_IP:8080
curl https://YOUR_EXTERNAL_IP:8443
```

## Troubleshooting

### Issue: DNS Still Points to Synology Web Interface
- Verify AdGuard Home DNS rewrites are correct
- Clear DNS cache on client devices
- Check if Nginx proxy is running and configured correctly

### Issue: SSL Certificate Errors
- Ensure Let's Encrypt certificates are valid for your domains
- If using Nginx proxy, configure SSL termination properly
- Check certificate paths and permissions

### Issue: Services Not Accessible
- Verify Traefik is running on correct ports: `docker ps`
- Check Traefik logs: `docker logs traefik`
- Ensure firewall rules allow internal access to ports 8080/8443

## Summary
The recommended approach is to use Nginx as a reverse proxy on the Synology NAS to handle the port conflict, allowing you to maintain the standard port 80/443 access while Traefik runs on alternative ports 8080/8443.
