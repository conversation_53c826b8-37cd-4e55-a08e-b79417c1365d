# Traefik Configuration for Internal-Only Access
# This configuration restricts access to local and VPN networks only
#
# IMPORTANT: Synology NAS Port Conflict Resolution
# - Synology NAS uses ports 80/443 and cannot be disabled
# - <PERSON><PERSON><PERSON><PERSON> will use ports 8080/8443 to avoid conflicts
# - DNS rewrites will point to *************:8080/8443
# - Network Topology:
#   - Wired LAN: ***********/24 (Synology NAS: *************)
#   - WiFi LAN: ***********/24
#   - Guest Network: **********/24 (BLOCKED from accessing <PERSON>rae<PERSON><PERSON>)
#   - VPN Network: ***********/24

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and Dashboard (restrict to internal networks)
api:
  dashboard: true
  debug: false

# Entry points - Using alternative ports to avoid Synology NAS conflict
entryPoints:
  web:
    address: ":8080"  # Changed from :80 to avoid Synology conflict
    # Restrict to internal networks only
    forwardedHeaders:
      trustedIPs:
        - "***********/24"    # Wired LAN
        - "***********/24"    # WiFi LAN
        - "***********/24"    # VPN network
        - "127.0.0.1/32"      # Localhost
        - "::1/128"           # IPv6 localhost
        # NOTE: **********/24 (guest network) is INTENTIONALLY EXCLUDED
  websecure:
    address: ":8443"  # Changed from :443 to avoid Synology conflict
    # Restrict to internal networks only
    forwardedHeaders:
      trustedIPs:
        - "***********/24"    # Wired LAN
        - "***********/24"    # WiFi LAN
        - "***********/24"    # VPN network
        - "127.0.0.1/32"      # Localhost
        - "::1/128"           # IPv6 localhost
        # NOTE: **********/24 (guest network) is INTENTIONALLY EXCLUDED

# Certificate resolvers (if using Let's Encrypt)
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>  # Replace with your email
      storage: /letsencrypt/acme.json
      # Use DNS challenge for internal-only setup
      dnsChallenge:
        provider: dynu  # Your DDNS provider
        delayBeforeCheck: 30
      # Alternative: HTTP challenge (only if temporarily allowing public access)
      # httpChallenge:
      #   entryPoint: web

# Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "traefik"
  file:
    directory: /etc/traefik/
    watch: true
    # This will load dynamic.yml, services-config.yml, and any other .yml files

# Logging
log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"

accessLog:
  filePath: "/var/log/traefik/access.log"

# Metrics (optional)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
