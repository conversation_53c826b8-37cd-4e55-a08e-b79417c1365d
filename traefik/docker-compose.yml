# Traefik Docker Compose Configuration
# Deploy this on your Synology NAS (192.168.1.113) via Portainer
#
# IMPORTANT: Port Configuration to Avoid Synology NAS Conflicts
# - Synology NAS uses ports 80/443 and cannot be disabled
# - Traefik uses ports 8080/8443 to avoid conflicts
# - DNS rewrites point subdomains to 192.168.1.113:8080/8443
# - Network includes both wired (192.168.1.0/24) and WiFi (192.168.2.0/24)

version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped

    # Security: Run as non-root user (adjust to your Synology user ID)
    user: "1026:100"  # Common Synology user:group IDs

    ports:
      # Using alternative ports to avoid Synology NAS conflicts
      - "8080:8080"   # HTTP (changed from 80)
      - "8443:8443"   # HTTPS (changed from 443)
      # Dashboard port (same as HTTP for simplicity)
      - "9080:9080"   # Alternative dashboard port
    
    environment:
      # DNS provider credentials (for Let's Encrypt DNS challenge)
      - DYNU_API_KEY=your_dynu_api_key  # Replace with your actual API key
      # Network configuration
      - TRAEFIK_LOG_LEVEL=INFO
    
    volumes:
      # Traefik configuration
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./dynamic.yml:/etc/traefik/dynamic.yml:ro
      
      # Docker socket (for auto-discovery)
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # SSL certificates storage
      - ./letsencrypt:/letsencrypt
      
      # Logs
      - ./logs:/var/log/traefik
    
    networks:
      - traefik
      - default
    
    labels:
      - "traefik.enable=true"
      
      # Dashboard
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.mdewaele.freeddns.org`)"
      - "traefik.http.routers.traefik-dashboard.tls=true"
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.middlewares=internal-only@file"
      
      # API
      - "traefik.http.routers.traefik-api.rule=Host(`traefik.mdewaele.freeddns.org`) && PathPrefix(`/api`)"
      - "traefik.http.routers.traefik-api.tls=true"
      - "traefik.http.routers.traefik-api.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-api.service=api@internal"
      - "traefik.http.routers.traefik-api.middlewares=internal-only@file"

  # Example service configuration
  # Uncomment and modify for your actual services
  
  # whoami:
  #   image: traefik/whoami
  #   container_name: whoami
  #   restart: unless-stopped
  #   networks:
  #     - traefik
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.whoami.rule=Host(`home.mdewaele.freeddns.org`)"
  #     - "traefik.http.routers.whoami.tls=true"
  #     - "traefik.http.routers.whoami.tls.certresolver=letsencrypt"
  #     - "traefik.http.routers.whoami.middlewares=internal-only@file"

networks:
  traefik:
    external: true
  default:
    driver: bridge
