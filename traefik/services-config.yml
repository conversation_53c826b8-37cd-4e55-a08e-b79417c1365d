# Traefik Services Configuration - COMPLETE SERVICE INVENTORY
# This file contains routing configuration for all containerized services
# All services use the internal-only middleware to block external and guest network access
#
# Network Security:
# - Only accessible from: ***********/24, ***********/24, ***********/24
# - BLOCKED from: Guest network (**********/24), External internet
# - Port Configuration: <PERSON><PERSON>fik uses 8080/8443 to avoid Synology NAS conflicts

http:
  routers:
    # ===== CORE INFRASTRUCTURE SERVICES =====
    
    # Home Assistant - Smart Home Controller
    home-router:
      rule: "Host(`home.mdewaele.freeddns.org`)"
      service: "home-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Traefik Dashboard - Load Balancer Management
    traefik-dashboard-router:
      rule: "Host(`traefik.mdewaele.freeddns.org`)"
      service: "api@internal"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Load Balancer Dashboard (Alternative)
    lb-router:
      rule: "Host(`lb.mdewaele.freeddns.org`)"
      service: "api@internal"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Portainer - Container Management
    portainer-router:
      rule: "Host(`portainer.mdewaele.freeddns.org`)"
      service: "portainer-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # AdGuard Home - DNS Management
    adguard-router:
      rule: "Host(`adguard.mdewaele.freeddns.org`)"
      service: "adguard-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # ===== HOME AUTOMATION & IOT SERVICES =====
    
    # Zigbee Controller - IoT Device Management
    zigbee-router:
      rule: "Host(`zigbee.mdewaele.freeddns.org`)"
      service: "zigbee-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # UniFi Network Controller - Network Management
    unifi-router:
      rule: "Host(`unifi.mdewaele.freeddns.org`)"
      service: "unifi-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # UniFi Hotspot Manager - Guest Network Management
    hotspot-router:
      rule: "Host(`hotspot.mdewaele.freeddns.org`)"
      service: "hotspot-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # ===== MEDIA & ENTERTAINMENT SERVICES =====
    
    # Jellyfin Media Server - Movies, TV Shows, Music
    jellyfin-router:
      rule: "Host(`jellyfin.mdewaele.freeddns.org`)"
      service: "jellyfin-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Custom Media Web Application
    media-router:
      rule: "Host(`media.mdewaele.freeddns.org`)"
      service: "media-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Media Web App API (PHP Backend)
    media-api-router:
      rule: "Host(`api.media.mdewaele.freeddns.org`)"
      service: "media-api-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
        - "api-headers@file"
      tls:
        certResolver: "letsencrypt"
    
    # Synology NAS Dashboard
    nas-router:
      rule: "Host(`nas.mdewaele.freeddns.org`)"
      service: "nas-service"
      middlewares:
        - "internal-only@file"
        - "secure-headers@file"
      tls:
        certResolver: "letsencrypt"

  services:
    # ===== SERVICE DEFINITIONS =====
    # Update ports to match your actual container configurations
    
    # Home Assistant Service
    home-service:
      loadBalancer:
        servers:
          - url: "http://*************:8123"  # Default Home Assistant port
    
    # Portainer Service
    portainer-service:
      loadBalancer:
        servers:
          - url: "http://*************:9000"  # Default Portainer port
    
    # AdGuard Home Service
    adguard-service:
      loadBalancer:
        servers:
          - url: "http://*************:3000"  # Default AdGuard Home port
    
    # Zigbee Controller Service
    zigbee-service:
      loadBalancer:
        servers:
          - url: "http://*************:8080"  # Update with actual port
    
    # UniFi Network Controller Service
    unifi-service:
      loadBalancer:
        servers:
          - url: "https://*************:8443"  # Default UniFi Controller HTTPS port
    
    # UniFi Hotspot Manager Service
    hotspot-service:
      loadBalancer:
        servers:
          - url: "http://*************:8880"  # Update with actual port
    
    # Jellyfin Media Server Service
    jellyfin-service:
      loadBalancer:
        servers:
          - url: "http://*************:8096"  # Default Jellyfin port
    
    # Custom Media Web Application Service
    media-service:
      loadBalancer:
        servers:
          - url: "http://*************:8081"  # Update with actual port
    
    # Media Web App API Service (PHP)
    media-api-service:
      loadBalancer:
        servers:
          - url: "http://*************:8082"  # Update with actual port
    
    # Synology NAS Service (DSM)
    nas-service:
      loadBalancer:
        servers:
          - url: "https://*************:5001"  # Synology DSM HTTPS port

  middlewares:
    # API-specific headers for media API
    api-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          Access-Control-Allow-Origin: "*"
          Access-Control-Allow-Methods: "GET,POST,PUT,DELETE,OPTIONS"
          Access-Control-Allow-Headers: "Content-Type,Authorization"

# ===== CONFIGURATION INSTRUCTIONS =====
# 1. Copy this configuration to your Traefik dynamic configuration directory
# 2. Update service ports to match your actual container configurations
# 3. Ensure all containers are running on Synology NAS (*************)
# 4. Verify internal-only middleware is defined in dynamic.yml
# 5. Test access via VPN connection only
#
# ===== SECURITY NOTES =====
# - All services use internal-only@file middleware
# - Guest network (**********/24) is BLOCKED by IP whitelist
# - External access is BLOCKED by EdgeRouter firewall
# - SSL/TLS certificates are automatically managed by Let's Encrypt
# - Services are accessible on Traefik ports 8080/8443 only
#
# ===== PORT REFERENCE =====
# Update these ports to match your actual container configurations:
# - Home Assistant: 8123 (default)
# - Portainer: 9000 (default)
# - AdGuard Home: 3000 (default)
# - Jellyfin: 8096 (default)
# - UniFi Controller: 8443 (default HTTPS)
# - Zigbee Controller: Update with actual port
# - Hotspot Manager: Update with actual port
# - Media App: Update with actual port
# - Media API: Update with actual port
# - Synology DSM: 5001 (default HTTPS)
