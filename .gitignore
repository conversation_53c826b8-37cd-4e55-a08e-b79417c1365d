# VPN-Only Access Configuration - .gitignore
# Excludes sensitive files, private keys, and system-specific configurations

# ===== SENSITIVE SECURITY FILES =====
# WireGuard private keys and sensitive configurations
*.key
*.pem
*.p12
*.pfx
*private*
*secret*
*password*

# WireGuard client configurations with embedded keys
clients/*-with-keys.conf
clients/generated-*.conf
clients/client-*.conf

# Certificate files
*.crt
*.cert
*.ca-bundle
*.p7b
*.p7s
*.der

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub
*.ppk

# ===== CONFIGURATION FILES WITH SECRETS =====
# Files that may contain sensitive information
config.yml
secrets.yml
.env
.env.local
.env.production
.env.staging

# Traefik configuration files with API keys or certificates
traefik/acme.json
traefik/dynamic-secrets.yml
traefik/letsencrypt/

# AdGuard Home configuration backups
dns/AdGuardHome.yaml
dns/adguard-backup-*.yaml

# ===== NETWORK-SPECIFIC FILES =====
# Files with actual IP addresses and network details
*-actual-ips.conf
*-production.conf
*-live.conf
network-mapping.txt
device-inventory.txt

# Customized configuration files
*-custom.conf
*-personalized.conf
*-modified.conf

# ===== BACKUP AND TEMPORARY FILES =====
# Configuration backups
*.backup
*.bak
*.old
*.orig
*~
.#*
#*#

# Temporary files
*.tmp
*.temp
*.swp
*.swo
.DS_Store
Thumbs.db

# Log files
*.log
*.log.*
logs/
log/

# ===== SYSTEM AND EDITOR FILES =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== EDITOR AND IDE FILES =====
# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Nano
*.save

# ===== DOCKER AND CONTAINER FILES =====
# Docker
.dockerignore
docker-compose.override.yml
docker-compose.prod.yml
.docker/

# Container volumes and data
volumes/
data/
storage/

# ===== TESTING AND DEVELOPMENT =====
# Test results and coverage
test-results/
coverage/
*.coverage
.nyc_output/

# Development databases
*.db
*.sqlite
*.sqlite3

# ===== DOCUMENTATION BUILDS =====
# Generated documentation
docs/_build/
docs/build/
site/
_site/

# ===== PACKAGE MANAGERS =====
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
pip-log.txt
pip-delete-this-directory.txt

# ===== PROJECT-SPECIFIC EXCLUSIONS =====
# Actual network configurations (keep templates only)
edgerouter/actual-*.conf
clients/actual-*.conf
dns/actual-*.conf

# Generated client configurations
clients/generated/
clients/output/

# Test output files
testing/test-output-*.txt
testing/results-*.log

# Personal notes and documentation
NOTES.md
TODO.md
PERSONAL.md
*-notes.txt

# Customization files
customizations/
personal/
local/

# ===== SECURITY SCANNER EXCLUSIONS =====
# Security scan results
*.sarif
security-scan-*.json
vulnerability-report-*.html

# ===== WHAT TO KEEP (COMMENTS FOR REFERENCE) =====
# These files SHOULD be included in the repository:
# - Template configuration files (*.conf.template)
# - Documentation files (*.md)
# - Shell scripts (*.sh)
# - Example configurations without sensitive data
# - README and implementation guides
# - Testing scripts (without actual credentials)

# ===== CUSTOM ADDITIONS =====
# Add any project-specific files to ignore below this line
# Example:
# my-custom-config.yml
# local-overrides/
/.idea/ScaScanState.xml
