# Complete Service Inventory - Optimized DS923+ Dual NIC LAG Configuration

## Overview
This document provides a comprehensive inventory of all services, devices, and configurations in the optimized VPN-only access setup featuring DS923+ dual NIC Link Aggregation and complete Ubiquiti switch infrastructure. All services are hosted on Synology DS923+ (*************) with 2 Gbps aggregate bandwidth and are accessible only via VPN connection.

## Optimized Network Architecture

### Complete Network Segmentation
- **VLAN 1** (***********/24): Core infrastructure & wired devices - DS923+ LAG: *************
- **VLAN 2** (**********/24): Guest network (WiFi only, completely isolated)
- **VLAN 3** (***********/24): Media network (DLNA optimized for Yamaha speakers)
- **VLAN 4** (***********/24): IoT trusted (Home Assistant access only)
- **VLAN 5** (***********/24): VPN network (full administrative access)
- **VLAN 6** (***********/24): IoT untrusted (internet only)
- **VLAN 10** (************/24): Primary WiFi users (full internal access)

### Enhanced Security Policy
- **VPN Access**: All services accessible via ***********/24 (VPN network) with full administrative rights
- **Wired Access**: All services accessible via ***********/24 (trusted wired devices)
- **WiFi Users**: All services accessible via ************/24 (primary WiFi network)
- **Media Network**: Limited access to media services via ***********/24
- **IoT Trusted**: Home Assistant access only via ***********/24
- **IoT Untrusted**: **COMPLETELY BLOCKED** from all internal services (***********/24)
- **Guest Network**: **COMPLETELY BLOCKED** from all internal services (**********/24)
- **External Access**: **COMPLETELY BLOCKED** from all internal services and devices

## Service Inventory

### Core Infrastructure Services
All services hosted on Synology NAS (*************):

| Service | Subdomain | Port | Description |
|---------|-----------|------|-------------|
| Home Assistant | home.mdewaele.freeddns.org | 8123 | Smart Home Controller |
| Traefik Dashboard | traefik.mdewaele.freeddns.org | 9080 | Reverse Proxy Management |
| Load Balancer | lb.mdewaele.freeddns.org | 9080 | Alternative Traefik Dashboard |
| AdGuard Home | adguard.mdewaele.freeddns.org | 3000 | DNS Server & Ad Blocker |
| Portainer | portainer.mdewaele.freeddns.org | 9000 | Container Management |
| Synology DSM | nas.mdewaele.freeddns.org | 5000/5001 | NAS Management Interface |

### Home Automation & IoT Services
| Service | Subdomain | Port | Description |
|---------|-----------|------|-------------|
| Jellyfin | jellyfin.mdewaele.freeddns.org | 8096 | Media Server (Movies, TV, Music) |
| Zigbee Controller | zigbee.mdewaele.freeddns.org | 8080* | IoT Device Management |
| UniFi Controller | unifi.mdewaele.freeddns.org | 8443 | Network Management |
| Hotspot Manager | hotspot.mdewaele.freeddns.org | 8880* | Guest Network Management |

### Media & Entertainment Services
| Service | Subdomain | Port | Description |
|---------|-----------|------|-------------|
| Media Web App | media.mdewaele.freeddns.org | 8081* | Custom Media Application |
| Media API | api.media.mdewaele.freeddns.org | 8082* | PHP Backend API |

*Note: Ports marked with * should be updated to match your actual container configurations.

### Static Device Mappings
Yamaha MusicCast Speakers (update IP addresses to match your devices):

| Device | Subdomain | IP Address | Description |
|--------|-----------|------------|-------------|
| Living Room Speaker | livingroom.mdewaele.freeddns.org | 192.168.1.XXX | Main living area audio |
| Kitchen Speaker | kitchen.mdewaele.freeddns.org | 192.168.1.XXX | Kitchen area audio |
| Bureau Speaker | bureau.mdewaele.freeddns.org | 192.168.1.XXX | Office/study area audio |
| Bedroom Speaker | bedroom.mdewaele.freeddns.org | 192.168.1.XXX | Bedroom area audio |

### Network Infrastructure
| Service | Subdomain | IP Address | Description |
|---------|-----------|------------|-------------|
| EdgeRouter | router.mdewaele.freeddns.org | *********** | Network Gateway & VPN Server |

## Port Configuration

### Traefik Port Mapping (Port Conflict Resolution)
- **HTTP Entry Point**: 8080 (avoids Synology NAS port 80 conflict)
- **HTTPS Entry Point**: 8443 (avoids Synology NAS port 443 conflict)
- **Dashboard**: 9080 (management interface)

### Service Access Methods
1. **Via Traefik (Recommended)**: `https://service.mdewaele.freeddns.org:8443`
2. **Direct Port Access**: `http://service.mdewaele.freeddns.org:8080`
3. **Nginx Reverse Proxy**: `https://service.mdewaele.freeddns.org` (if configured)

### Blocked Ports (External & Guest Network)
All these ports are blocked from external internet and guest network access:
- **80, 443**: Standard HTTP/HTTPS
- **3000**: AdGuard Home
- **5000, 5001**: Synology DSM
- **8080, 8081, 8082**: Custom applications
- **8096**: Jellyfin
- **8123**: Home Assistant
- **8443**: Traefik HTTPS / UniFi Controller
- **8880**: Hotspot Manager
- **9000**: Portainer
- **9080**: Traefik Dashboard

## DNS Configuration

### AdGuard Home DNS Rewrites
All subdomains resolve to appropriate IP addresses when connected via VPN:

**Containerized Services** → ************* (Synology NAS)
**Static Devices** → Individual device IP addresses
**EdgeRouter** → ***********

### DNS Security
- **Internal DNS**: ************* (AdGuard Home on Synology NAS)
- **Guest Network DNS**: Public DNS only (*******, *******)
- **VPN Client DNS**: ************* (internal DNS with ad blocking)

## Security Implementation

### Firewall Rules
1. **WAN_IN**: Blocks all service ports from external internet
2. **GUEST_IN**: Blocks all internal network access from guest network
3. **VPN_IN**: Allows VPN clients to access internal networks

### IP Whitelisting (Traefik)
- **Allowed**: ***********/24, ***********/24, ***********/24
- **Blocked**: **********/24 (guest network), external internet

### SSL/TLS Certificates
- **Provider**: Let's Encrypt via Dynu DNS challenge
- **Automatic Renewal**: Configured for all services
- **HTTPS Redirect**: Enabled for all services

## Testing & Verification

### External Access Tests
All services should be **BLOCKED** from external internet:
```bash
./testing/test-external-access.sh
```

### VPN Access Tests
All services should be **ACCESSIBLE** via VPN:
```bash
./testing/test-vpn-access.sh
```

### Guest Network Isolation Tests
All services should be **BLOCKED** from guest network:
```bash
./testing/test-guest-network-isolation.sh
```

## Configuration Files

### DNS Configuration
- `dns/adguard-custom-dns.conf`: Complete DNS rewrite configuration

### Traefik Configuration
- `traefik/traefik.yml`: Main Traefik configuration with port conflict resolution
- `traefik/dynamic.yml`: IP whitelisting and security middleware
- `traefik/services-config.yml`: Complete service routing configuration

### Security Configuration
- `security/firewall-rules.conf`: Comprehensive EdgeRouter firewall rules
- `security/guest-network-isolation.conf`: Guest network isolation configuration

### Testing Scripts
- `testing/test-external-access.sh`: Verify external blocking for all services
- `testing/test-vpn-access.sh`: Verify VPN access for all services
- `testing/test-guest-network-isolation.sh`: Verify guest network isolation

## Implementation Checklist

### Phase 1: DNS Configuration
- [ ] Add all DNS rewrites to AdGuard Home
- [ ] Update static device IP addresses
- [ ] Test DNS resolution via VPN

### Phase 2: Traefik Configuration
- [ ] Deploy services-config.yml
- [ ] Update service ports to match containers
- [ ] Apply internal-only middleware to all services
- [ ] Test service access via Traefik

### Phase 3: Security Configuration
- [ ] Deploy comprehensive firewall rules
- [ ] Configure guest network isolation
- [ ] Test external access blocking
- [ ] Test guest network isolation

### Phase 4: Verification
- [ ] Run all testing scripts
- [ ] Verify VPN-only access for all services
- [ ] Confirm guest network isolation
- [ ] Test static device access

## Maintenance

### Regular Tasks
1. **Monitor firewall logs** for blocked access attempts
2. **Update service ports** when container configurations change
3. **Test access controls** monthly using provided scripts
4. **Review DNS rewrites** when adding new services
5. **Update static device IPs** when devices change

### Adding New Services
1. Add DNS rewrite to AdGuard Home configuration
2. Add service configuration to Traefik services-config.yml
3. Apply internal-only middleware
4. Update firewall rules if using non-standard ports
5. Add to testing scripts
6. Update this service inventory

## Security Notes

### Critical Security Features
- **Zero Trust Guest Network**: Complete isolation from all internal resources
- **VPN-Only Access**: No public exposure of any internal services
- **Comprehensive Monitoring**: All blocked attempts are logged
- **Automatic SSL**: All services use HTTPS with automatic certificate renewal

### Security Verification
- All services accessible only via VPN (***********/24) or internal networks
- Guest network (**********/24) completely isolated from all internal resources
- External internet access completely blocked for all service ports
- Static devices protected from guest network access

This configuration provides enterprise-level security for your home network while maintaining full functionality for authorized users via VPN connection.
