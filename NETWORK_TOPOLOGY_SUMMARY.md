# Network Topology Summary - Optimized DS923+ Dual NIC LAG Configuration

## Final Optimized Network Architecture

This document summarizes the complete optimized network topology featuring Synology DS923+ dual NIC Link Aggregation and complete Ubiquiti switch infrastructure for maximum performance and security.

## Complete Hardware Infrastructure

### Physical Network Layout
- **EdgeRouter 4**: Primary gateway and VPN server (***********)
- **USW Flex 2.5G**: Core managed switch with VLAN routing (192.168.1.2)
- **Synology DS923+**: Dual NIC LAG - 2 Gbps aggregate bandwidth (*************)
- **24-Port Unmanaged Switch**: Access layer for wired devices (192.168.1.5)
- **4-Port PoE Switch**: Dedicated for UniFi access points (192.168.1.6)
- **UniFi AC Pro**: Primary WiFi coverage - WiFi 5 (192.168.1.3)
- **UniFi U6 Lite**: High-performance WiFi - WiFi 6 (192.168.1.4)
- **UniFi Controller**: Containerized on Synology NAS

### Optimized Network Segmentation
- **VLAN 1** (***********/24): Core infrastructure & all wired devices (trusted)
- **VLAN 2** (172.16.1.0/24): Guest network (WiFi only, completely isolated)
- **VLAN 3** (192.168.3.0/24): Media network (wired speakers + WiFi, DLNA optimized)
- **VLAN 4** (192.168.4.0/24): IoT trusted (WiFi only, Home Assistant access)
- **VLAN 5** (***********/24): VPN network (full administrative access)
- **VLAN 6** (192.168.6.0/24): IoT untrusted (WiFi only, internet only)
- **VLAN 10** (192.168.10.0/24): Primary WiFi users (full internal access)

### DS923+ Link Aggregation Configuration
The Synology DS923+ uses both network ports in Link Aggregation (LAG) mode:
- **Port 1**: Connected to USW Flex Port 2 (LAG Member 1)
- **Port 2**: Connected to USW Flex Port 3 (LAG Member 2)
- **LAG Interface**: 2 Gbps aggregate bandwidth with automatic load balancing
- **Redundancy**: Automatic failover if one port fails
- **Performance**: 100% improvement over single NIC configuration

### Complete Switch Infrastructure Security
All network segments are completely isolated with controlled access:
- **Guest Network (VLAN 2)**: Internet only, blocked from all internal resources
- **IoT Untrusted (VLAN 6)**: Internet only, no internal network access
- **IoT Trusted (VLAN 4)**: Limited access to Home Assistant and Zigbee only
- **Media Network (VLAN 3)**: Access to media services and DLNA streaming
- **WiFi Users (VLAN 10)**: Full access to internal services
- **Wired Devices (VLAN 1)**: Full trusted access to all resources
- **VPN Network (VLAN 5)**: Complete administrative access to all networks
- **BLOCKED**: Access to WireGuard VPN server
- **ALLOWED**: Internet access only via NAT

## Port Conflict Issue & Resolution

### The Problem
- **Synology NAS** uses ports 80/443 for its built-in web interface (DSM)
- These ports **cannot be disabled** on Synology NAS
- **Traefik** traditionally uses ports 80/443 for HTTP/HTTPS traffic
- **DNS rewrites** point subdomains to *************
- **Conflict**: Requests to subdomains hit Synology web interface instead of Traefik

### The Solution
**Traefik Port Reassignment:**
- HTTP: Port 8080 (instead of 80)
- HTTPS: Port 8443 (instead of 443)
- Dashboard: Port 9080 (additional management port)

**Access Methods:**
1. **Direct Port Access**: `http://home.mdewaele.freeddns.org:8080`
2. **Nginx Reverse Proxy** (Recommended): Configure Nginx on Synology to proxy ports 80/443 to 8080/8443
3. **Secondary IP**: Assign additional IP to Synology for Traefik

## Configuration Updates Made

### 1. EdgeRouter Configuration
- **Network Groups**: Added support for both ***********/24 and ***********/24
- **Firewall Rules**: Block external access to ports 8080, 8443, 9080 (Traefik ports)
- **Port Groups**: Separate groups for standard ports, Traefik ports, and Synology ports
- **VPN Routing**: Allow VPN clients to access both wired and WiFi networks

### 2. Traefik Configuration
- **Entry Points**: Changed from :80/:443 to :8080/:8443
- **IP Whitelisting**: Include ***********/24, ***********/24, and ***********/24
- **Docker Compose**: Updated port mappings and user permissions for Synology
- **TLS Configuration**: Maintained SSL/TLS support on alternative ports

### 3. DNS Configuration
- **AdGuard Home**: All DNS rewrites point to *************
- **EdgeRouter DNS**: Forward to ************* (AdGuard on Synology)
- **DHCP Settings**: Both wired and WiFi networks use ************* as DNS server
- **Hosts File Backup**: All entries point to *************

### 4. WireGuard Client Configuration
- **DNS Server**: ************* (AdGuard Home on Synology)
- **Allowed IPs**: ***********/24, ***********/24, ***********/24
- **Routing**: Access to both wired and WiFi networks via VPN

### 5. Testing Scripts
- **External Tests**: Verify blocking of ports 8080, 8443, 9080, 3000, 5000, 5001
- **VPN Tests**: Test connectivity to Synology NAS and all service ports
- **DNS Tests**: Verify resolution to ************* for all subdomains
- **Service Tests**: Test both Traefik and Synology DSM accessibility

## Service Access Summary

### External Access (Should be BLOCKED)
- ❌ Port 80 (Synology HTTP)
- ❌ Port 443 (Synology HTTPS)
- ❌ Port 8080 (Traefik HTTP)
- ❌ Port 8443 (Traefik HTTPS)
- ❌ Port 9080 (Traefik Dashboard)
- ❌ Port 3000 (AdGuard Home)
- ❌ Port 5000/5001 (Synology DSM)
- ✅ Port 51820 (WireGuard VPN) - MUST be accessible

### Internal/VPN Access (Should be ACCESSIBLE)
- ✅ http://*************:80 (Synology DSM)
- ✅ https://*************:443 (Synology DSM)
- ✅ http://*************:8080 (Traefik HTTP)
- ✅ https://*************:8443 (Traefik HTTPS)
- ✅ http://*************:9080 (Traefik Dashboard)
- ✅ http://*************:3000 (AdGuard Home)
- ✅ http://*************:5000 (Synology DSM HTTP)
- ✅ https://*************:5001 (Synology DSM HTTPS)

### Subdomain Access (Via VPN)
With DNS rewrites pointing to *************:
- `home.mdewaele.freeddns.org` → *************
- `traefik.mdewaele.freeddns.org` → *************
- `adguard.mdewaele.freeddns.org` → *************
- `router.mdewaele.freeddns.org` → ***********
- `nas.mdewaele.freeddns.org` → *************

**Access Methods:**
1. **With Port Numbers**: `http://home.mdewaele.freeddns.org:8080`
2. **With Nginx Proxy**: `http://home.mdewaele.freeddns.org` (if Nginx configured)
3. **Direct IP**: `http://*************:8080`

## Implementation Priority

### High Priority (Security Critical)
1. ✅ Update EdgeRouter firewall rules to block all service ports externally
2. ✅ Configure Traefik to use alternative ports (8080/8443)
3. ✅ Update DNS rewrites to point to *************
4. ✅ Test external blocking of all service ports

### Medium Priority (Functionality)
1. Configure Nginx reverse proxy on Synology (recommended for seamless access)
2. Update all service configurations to use internal-only middleware
3. Test VPN access to all services
4. Verify SSL certificates work on alternative ports

### Low Priority (Convenience)
1. Set up monitoring for port conflicts
2. Create automated testing scripts
3. Document troubleshooting procedures
4. Plan for certificate renewal on alternative ports

## Next Steps

1. **Review** all configuration files have been updated with correct IPs
2. **Implement** the EdgeRouter and Traefik configurations
3. **Test** external blocking using the provided test scripts
4. **Configure** Nginx reverse proxy for seamless subdomain access (optional but recommended)
5. **Verify** VPN access to all services
6. **Monitor** for any remaining port conflicts or access issues

## Files Updated

All configuration files have been updated to reflect this network topology:
- `edgerouter/` - Updated for dual LAN support and Synology IP
- `clients/` - Updated DNS and allowed IPs for all networks
- `traefik/` - Updated ports and IP whitelisting
- `dns/` - Updated to use ************* for all services
- `security/` - Updated firewall rules for all networks and ports
- `testing/` - Updated test scripts for correct IPs and ports
- `README.md` and `IMPLEMENTATION_GUIDE.md` - Updated documentation
