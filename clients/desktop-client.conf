# WireGuard Client Configuration - Desktop/Laptop
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: ***********/24
#
# Replace placeholders with actual values:
# - CLIENT_PRIVATE_KEY: Generate with 'wg genkey'
# - CLIENT_PUBLIC_KEY: Generate with 'echo CLIENT_PRIVATE_KEY | wg pubkey'
# - SERVER_PUBLIC_KEY: Get from EdgeRouter (/config/auth/wireguard/server-public.key)
# - YOUR_EXTERNAL_IP: Your home's external IP address or DDNS hostname

[Interface]
# Client's private key (generate with: wg genkey)
PrivateKey = CLIENT_PRIVATE_KEY

# Client's VPN IP address
Address = ***********/24

# DNS servers - use AdGuard Home on Synology NAS for ad blocking and local resolution
DNS = *************

# Optional: Prevent DNS leaks with fallback DNS
# DNS = *************, *******

[Peer]
# Server's public key (from EdgeRouter)
PublicKey = SERVER_PUBLIC_KEY

# Server endpoint - your external IP/hostname and WireGuard port
Endpoint = YOUR_EXTERNAL_IP:51820

# Allowed IPs - route only local networks through VPN
# This configuration routes only local traffic through VPN
AllowedIPs = ***********/24, ***********/24, ***********/24

# Optional: Route all traffic through VPN (less secure for general browsing)
# AllowedIPs = 0.0.0.0/0

# Keep connection alive
PersistentKeepalive = 25

# Optional: Pre/Post connection scripts (Linux/macOS)
# PostUp = echo 'nameserver *************' | resolvconf -a tun.%i -m 0 -x
# PreDown = resolvconf -d tun.%i
