# WireGuard Client Configuration - Mobile Device (iOS/Android)
# Network Topology:
# - Wired LAN: ***********/24 (Synology NAS: *************)
# - WiFi LAN: ***********/24
# - VPN Network: ***********/24
#
# Replace placeholders with actual values:
# - CLIENT_PRIVATE_KEY: Generate with 'wg genkey'
# - CLIENT_PUBLIC_KEY: Generate with 'echo CLIENT_PRIVATE_KEY | wg pubkey'
# - SERVER_PUBLIC_KEY: Get from EdgeRouter (/config/auth/wireguard/server-public.key)
# - YOUR_EXTERNAL_IP: Your home's external IP address or DDNS hostname

[Interface]
# Client's private key (generate with: wg genkey)
PrivateKey = CLIENT_PRIVATE_KEY

# Client's VPN IP address (use different IP for each client)
Address = ***********/24

# DNS servers - use AdGuard Home on Synology NAS for ad blocking
DNS = *************

[Peer]
# Server's public key (from EdgeRouter)
PublicKey = SERVER_PUBLIC_KEY

# Server endpoint - your external IP/hostname and WireGuard port
Endpoint = YOUR_EXTERNAL_IP:51820

# Allowed IPs - route only local networks through VPN
# This ensures only local traffic goes through VPN, internet traffic uses mobile data/WiFi
AllowedIPs = ***********/24, ***********/24, ***********/24

# Alternative: Route all traffic through VPN (use more battery, but more secure)
# AllowedIPs = 0.0.0.0/0

# Keep connection alive (important for mobile)
PersistentKeepalive = 25
