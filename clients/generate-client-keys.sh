#!/bin/bash
# Script to generate WireGuard client keys and configurations

echo "=== WireGuard Client Key Generation ==="
echo ""

# Check if WireGuard tools are installed
if ! command -v wg &> /dev/null; then
    echo "WireGuard tools not found. Please install:"
    echo "Ubuntu/Debian: sudo apt install wireguard-tools"
    echo "macOS: brew install wireguard-tools"
    echo "Windows: Download from https://www.wireguard.com/install/"
    exit 1
fi

# Function to generate client configuration
generate_client_config() {
    local client_name=$1
    local client_ip=$2
    local server_public_key=$3
    local server_endpoint=$4
    local adguard_ip=$5
    
    echo "Generating configuration for: $client_name"
    
    # Generate client keys
    client_private_key=$(wg genkey)
    client_public_key=$(echo "$client_private_key" | wg pubkey)
    
    echo "Client Name: $client_name"
    echo "Client Private Key: $client_private_key"
    echo "Client Public Key: $client_public_key"
    echo "Client VPN IP: $client_ip"
    echo ""
    
    # Create client configuration file
    cat > "${client_name}.conf" << EOF
# WireGuard Client Configuration - $client_name
# Generated on $(date)

[Interface]
PrivateKey = $client_private_key
Address = $client_ip/24
DNS = $adguard_ip

[Peer]
PublicKey = $server_public_key
Endpoint = $server_endpoint:51820
AllowedIPs = ***********/24, ***********/24, ***********/24
PersistentKeepalive = 25
EOF
    
    echo "Configuration saved to: ${client_name}.conf"
    echo ""
    echo "Add this peer to EdgeRouter with:"
    echo "configure"
    echo "set interfaces wireguard wg0 peer $client_public_key allowed-ips $client_ip/32"
    echo "set interfaces wireguard wg0 peer $client_public_key description '$client_name'"
    echo "commit"
    echo "save"
    echo "exit"
    echo ""
    echo "=================================="
    echo ""
}

# Get server information
echo "Enter your server details:"
read -p "Server Public Key (from EdgeRouter): " server_public_key
read -p "Server Endpoint (external IP or DDNS): " server_endpoint
echo "Using AdGuard Home on Synology NAS: *************"
adguard_ip="*************"

echo ""
echo "Generating client configurations..."
echo ""

# Generate configurations for different clients
generate_client_config "desktop-laptop" "***********" "$server_public_key" "$server_endpoint" "$adguard_ip"
generate_client_config "mobile-phone" "***********" "$server_public_key" "$server_endpoint" "$adguard_ip"
generate_client_config "tablet" "***********" "$server_public_key" "$server_endpoint" "$adguard_ip"

echo "All client configurations generated!"
echo ""
echo "Next steps:"
echo "1. Add each peer to your EdgeRouter using the commands shown above"
echo "2. Import the .conf files into your WireGuard clients"
echo "3. Test the connections"
