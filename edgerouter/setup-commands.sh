#!/bin/bash
# EdgeRouter 4 WireGuard Setup Commands
# Network Topology:
# - Wired LAN: 192.168.1.0/24 (eth1) - Synology NAS: 192.168.1.113
# - WiFi LAN: 192.168.2.0/24 (eth2 or separate AP)
# - Guest Network: 172.16.1.0/24 (VLAN 2 - Internet only, isolated)
# - VPN Network: ***********/24 (wg0)

echo "=== EdgeRouter 4 WireGuard VPN Setup with Guest Network Isolation ==="
echo "Network Configuration:"
echo "- Wired LAN: 192.168.1.0/24 (Synology NAS: 192.168.1.113)"
echo "- WiFi LAN: 192.168.2.0/24"
echo "- Guest Network: 172.16.1.0/24 (ISOLATED - Internet only)"
echo "- VPN Network: ***********/24"
echo ""
echo "SECURITY: Guest network will be completely isolated from internal networks"
echo ""
echo "Run these commands one by one on your EdgeRouter via SSH"
echo ""

echo "1. Generate WireGuard keys:"
echo "sudo mkdir -p /config/auth/wireguard"
echo "cd /config/auth/wireguard"
echo "wg genkey | tee server-private.key | wg pubkey > server-public.key"
echo "chmod 600 server-private.key"
echo "chmod 644 server-public.key"
echo ""

echo "2. Configure WireGuard interface:"
echo "configure"
echo "set interfaces wireguard wg0 address ***********/24"
echo "set interfaces wireguard wg0 description 'WireGuard VPN Server'"
echo "set interfaces wireguard wg0 listen-port 51820"
echo "set interfaces wireguard wg0 private-key /config/auth/wireguard/server-private.key"
echo ""

echo "3. Configure firewall rules:"
echo "# WAN_IN rules (traffic from internet to internal network)"
echo "set firewall name WAN_IN default-action drop"
echo "set firewall name WAN_IN description 'WAN to internal'"
echo "set firewall name WAN_IN rule 10 action accept"
echo "set firewall name WAN_IN rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_IN rule 10 destination port 51820"
echo "set firewall name WAN_IN rule 10 protocol udp"
echo "set firewall name WAN_IN rule 20 action drop"
echo "set firewall name WAN_IN rule 20 description 'Block HTTP/HTTPS from WAN'"
echo "set firewall name WAN_IN rule 20 destination port 80,443"
echo "set firewall name WAN_IN rule 20 protocol tcp"
echo "set firewall name WAN_IN rule 30 action accept"
echo "set firewall name WAN_IN rule 30 description 'Allow established/related'"
echo "set firewall name WAN_IN rule 30 state established enable"
echo "set firewall name WAN_IN rule 30 state related enable"
echo ""

echo "# WAN_LOCAL rules (traffic from internet to router itself)"
echo "set firewall name WAN_LOCAL default-action drop"
echo "set firewall name WAN_LOCAL description 'WAN to router'"
echo "set firewall name WAN_LOCAL rule 10 action accept"
echo "set firewall name WAN_LOCAL rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_LOCAL rule 10 destination port 51820"
echo "set firewall name WAN_LOCAL rule 10 protocol udp"
echo "set firewall name WAN_LOCAL rule 20 action accept"
echo "set firewall name WAN_LOCAL rule 20 description 'Allow SSH'"
echo "set firewall name WAN_LOCAL rule 20 destination port 22"
echo "set firewall name WAN_LOCAL rule 20 protocol tcp"
echo "set firewall name WAN_LOCAL rule 30 action accept"
echo "set firewall name WAN_LOCAL rule 30 description 'Allow established/related'"
echo "set firewall name WAN_LOCAL rule 30 state established enable"
echo "set firewall name WAN_LOCAL rule 30 state related enable"
echo ""

echo "4. Apply firewall to interfaces:"
echo "set interfaces ethernet eth0 firewall in name WAN_IN"
echo "set interfaces ethernet eth0 firewall local name WAN_LOCAL"
echo "# Apply to both LAN interfaces if separate"
echo "# set interfaces ethernet eth1 firewall in name LAN_IN"
echo "# set interfaces ethernet eth2 firewall in name LAN_IN"
echo ""

echo "5. Configure NAT for VPN clients:"
echo "set service nat rule 100 description 'VPN clients to internet'"
echo "set service nat rule 100 outbound-interface eth0"
echo "set service nat rule 100 source address ***********/24"
echo "set service nat rule 100 type masquerade"
echo ""

echo "6. IMPORTANT: Remove existing port forwarding rules (if any):"
echo "# List current port forwarding rules:"
echo "show service nat rule"
echo "# Delete port forwarding rules (replace X with rule numbers):"
echo "# delete service nat rule X"
echo ""

echo "7. Commit and save configuration:"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "8. Add client peers (run for each client):"
echo "# Replace CLIENT_PUBLIC_KEY and CLIENT_IP with actual values"
echo "configure"
echo "set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY allowed-ips CLIENT_IP/32"
echo "set interfaces wireguard wg0 peer CLIENT_PUBLIC_KEY description 'Client Device Name'"
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Key Generation for Reference ==="
echo "Server public key will be needed for client configurations:"
echo "cat /config/auth/wireguard/server-public.key"
