# EdgeRouter 4 WireGuard Server Configuration
# Network Topology:
# - Wired LAN: ***********/24 (eth1)
# - WiFi LAN: ***********/24 (eth2 or separate AP)
# - Guest Network: **********/24 (VLAN 2 - Internet only, isolated from internal networks)
# - VPN Network: ***********/24 (wg0)
# - Synology NAS: ************* (hosting Traefik, AdGuard, etc.)

# WireGuard Interface Configuration
interfaces {
    wireguard wg0 {
        address ***********/24
        description "WireGuard VPN Server"
        listen-port 51820
        private-key /config/auth/wireguard/server-private.key

        # Client peer configurations will be added here
        # Example peer (replace with actual client public keys):
        # peer <CLIENT_PUBLIC_KEY> {
        #     allowed-ips ***********/32
        #     description "Client Device Name"
        # }
    }
}

# Firewall Configuration
firewall {
    name WAN_IN {
        default-action drop
        description "WAN to internal"
        
        # Allow WireGuard VPN traffic
        rule 10 {
            action accept
            description "Allow WireGuard VPN"
            destination {
                port 51820
            }
            protocol udp
        }
        
        # Block all other traffic to internal services
        rule 20 {
            action drop
            description "Block HTTP traffic from WAN"
            destination {
                port 80,443
            }
            protocol tcp
        }
        
        # Allow established and related connections
        rule 30 {
            action accept
            description "Allow established/related"
            state {
                established enable
                related enable
            }
        }
    }
    
    name WAN_LOCAL {
        default-action drop
        description "WAN to router"
        
        # Allow WireGuard VPN
        rule 10 {
            action accept
            description "Allow WireGuard VPN"
            destination {
                port 51820
            }
            protocol udp
        }
        
        # Allow SSH (optional, consider restricting to VPN only)
        rule 20 {
            action accept
            description "Allow SSH"
            destination {
                port 22
            }
            protocol tcp
        }
        
        # Allow established and related
        rule 30 {
            action accept
            description "Allow established/related"
            state {
                established enable
                related enable
            }
        }
    }
    
    # VPN to LAN access rules
    name VPN_TO_LAN {
        default-action accept
        description "VPN clients to LAN"

        # Allow VPN clients to access wired LAN services
        rule 10 {
            action accept
            description "Allow VPN to wired LAN services"
            source {
                address ***********/24
            }
            destination {
                address ***********/24
            }
        }

        # Allow VPN clients to access WiFi LAN services
        rule 20 {
            action accept
            description "Allow VPN to WiFi LAN services"
            source {
                address ***********/24
            }
            destination {
                address ***********/24
            }
        }
    }

    # Guest network isolation rules
    name GUEST_IN {
        default-action drop
        description "Guest network isolation - Internet only"

        # BLOCK access to wired LAN
        rule 10 {
            action drop
            description "Block guest access to wired LAN"
            source {
                address **********/24
            }
            destination {
                address ***********/24
            }
            log enable
        }

        # BLOCK access to WiFi LAN
        rule 20 {
            action drop
            description "Block guest access to WiFi LAN"
            source {
                address **********/24
            }
            destination {
                address ***********/24
            }
            log enable
        }

        # BLOCK access to VPN network
        rule 30 {
            action drop
            description "Block guest access to VPN network"
            source {
                address **********/24
            }
            destination {
                address ***********/24
            }
            log enable
        }

        # BLOCK access to EdgeRouter management
        rule 40 {
            action drop
            description "Block guest access to EdgeRouter"
            source {
                address **********/24
            }
            destination {
                address ***********/32
            }
            log enable
        }

        # Allow established and related connections (for internet access)
        rule 50 {
            action accept
            description "Allow established/related for internet"
            state {
                established enable
                related enable
            }
        }

        # Allow DNS to public servers only (block internal DNS)
        rule 60 {
            action accept
            description "Allow DNS to public servers"
            destination {
                port 53
            }
            protocol udp
            # This rule allows DNS but internal DNS blocking is handled by destination rules
        }
    }
}

# NAT Configuration
nat {
    source {
        # NAT for VPN clients to access internet
        rule 100 {
            description "VPN clients to internet"
            outbound-interface eth0
            source {
                address ***********/24
            }
            translation {
                address masquerade
            }
        }
    }
}

# Static Routes (if needed)
protocols {
    static {
        # Route VPN traffic to WireGuard interface
        route ***********/24 {
            next-hop-interface wg0
        }

        # Ensure routing between networks (if needed)
        # route ***********/24 {
        #     next-hop-interface eth1
        # }
        # route ***********/24 {
        #     next-hop-interface eth2
        # }
    }
}

# Port forwarding rules (REMOVE these to block public access)
# Comment out or remove any existing port forwarding rules like:
# port-forward {
#     auto-firewall enable
#     hairpin-nat enable
#     lan-interface eth1
#     wan-interface eth0
#     rule 1 {
#         description "HTTP to Traefik"
#         forward-to {
#             address <TRAEFIK_IP>
#             port 80
#         }
#         original-port 80
#         protocol tcp
#     }
#     rule 2 {
#         description "HTTPS to Traefik"
#         forward-to {
#             address <TRAEFIK_IP>
#             port 443
#         }
#         original-port 443
#         protocol tcp
#     }
# }
