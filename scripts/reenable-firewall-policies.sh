#!/bin/bash

# EdgeRouter Firewall Policy Re-enablement Script
# This script re-enables and properly applies all firewall policies to interfaces
# Based on your Ubiquiti infrastructure with VLANs and VPN-only access

echo "=== EdgeRouter Firewall Policy Re-enablement ==="
echo "Re-enabling comprehensive firewall policies for VPN-only access"
echo ""

echo "Current firewall policies detected:"
echo "- GUEST_IN (6 rules)"
echo "- IOT_TRUSTED_IN (8 rules)" 
echo "- IOT_UNTRUSTED_IN (5 rules)"
echo "- MEDIA_IN (6 rules)"
echo "- VPN_IN (1 rule)"
echo "- WAN_IN (5 rules)"
echo "- WAN_LOCAL (2 rules)"
echo "- WIFI_USERS_IN (6 rules)"
echo ""

echo "=== Step 1: Apply WAN Interface Firewall Policies ==="
echo "Applying WAN_IN and WAN_LOCAL to eth0 (WAN interface):"
echo ""
echo "configure"
echo "set interfaces ethernet eth0 firewall in name WAN_IN"
echo "set interfaces ethernet eth0 firewall local name WAN_LOCAL"
echo ""

echo "=== Step 2: Apply VLAN Interface Firewall Policies ==="
echo "Applying firewall policies to VLAN interfaces on eth1:"
echo ""

echo "# VLAN 2 - Guest Network (172.16.1.0/24)"
echo "set interfaces ethernet eth1 vif 2 firewall in name GUEST_IN"
echo ""

echo "# VLAN 3 - Media Network (192.168.3.0/24)"
echo "set interfaces ethernet eth1 vif 3 firewall in name MEDIA_IN"
echo ""

echo "# VLAN 4 - IoT Trusted (192.168.4.0/24)"
echo "set interfaces ethernet eth1 vif 4 firewall in name IOT_TRUSTED_IN"
echo ""

echo "# VLAN 6 - IoT Untrusted (192.168.6.0/24)"
echo "set interfaces ethernet eth1 vif 6 firewall in name IOT_UNTRUSTED_IN"
echo ""

echo "# VLAN 10 - WiFi Users (192.168.10.0/24)"
echo "set interfaces ethernet eth1 vif 10 firewall in name WIFI_USERS_IN"
echo ""

echo "=== Step 3: Apply VPN Interface Firewall Policy ==="
echo "Applying VPN_IN to WireGuard interface:"
echo ""
echo "set interfaces wireguard wg0 firewall in name VPN_IN"
echo ""

echo "=== Step 4: Verify Interface Assignments ==="
echo "Checking that all interfaces have proper firewall assignments:"
echo ""
echo "show interfaces ethernet eth0 firewall"
echo "show interfaces ethernet eth1 vif 2 firewall"
echo "show interfaces ethernet eth1 vif 3 firewall"
echo "show interfaces ethernet eth1 vif 4 firewall"
echo "show interfaces ethernet eth1 vif 6 firewall"
echo "show interfaces ethernet eth1 vif 10 firewall"
echo "show interfaces wireguard wg0 firewall"
echo ""

echo "=== Step 5: Commit and Save Configuration ==="
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Step 6: Verification Commands ==="
echo "After applying, verify with these commands:"
echo ""
echo "# Check firewall rule status"
echo "show firewall statistics"
echo ""
echo "# Check specific policy details"
echo "show firewall name WAN_IN"
echo "show firewall name WAN_LOCAL"
echo "show firewall name GUEST_IN"
echo "show firewall name VPN_IN"
echo ""
echo "# Check interface assignments"
echo "show interfaces"
echo ""
echo "# Monitor firewall logs"
echo "show log firewall"
echo ""

echo "=== Security Policy Summary ==="
echo "✅ WAN_IN: Blocks all external access except WireGuard (port 51820)"
echo "✅ WAN_LOCAL: Allows only WireGuard and SSH to router"
echo "✅ GUEST_IN: Complete isolation - internet only access"
echo "✅ MEDIA_IN: DLNA optimized access to media services"
echo "✅ IOT_TRUSTED_IN: Home Assistant and Zigbee access"
echo "✅ IOT_UNTRUSTED_IN: Internet only, no internal access"
echo "✅ WIFI_USERS_IN: Core services access, UniFi devices blocked"
echo "✅ VPN_IN: Full access to all networks and management"
echo ""

echo "=== Next Steps ==="
echo "1. Connect to EdgeRouter via SSH or web interface"
echo "2. Run the configuration commands above"
echo "3. Test VPN connectivity"
echo "4. Verify services are only accessible via VPN"
echo "5. Test guest network isolation"
echo ""

echo "=== Emergency Access ==="
echo "If you lose access after applying firewall rules:"
echo "1. Connect directly via console cable"
echo "2. Or temporarily disable firewall: 'set firewall all-ping enable'"
echo "3. Or remove interface assignments: 'delete interfaces ethernet eth0 firewall'"
echo ""
