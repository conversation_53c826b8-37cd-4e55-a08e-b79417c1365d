#!/bin/bash

# EdgeRouter Firewall Status Diagnostic Script
# This script checks the current firewall configuration and interface assignments

echo "=== EdgeRouter Firewall Status Check ==="
echo "Checking current firewall policies and interface assignments"
echo ""

echo "=== Current Firewall Policies ==="
echo "show firewall"
echo ""

echo "=== Interface Firewall Assignments ==="
echo "show interfaces ethernet eth0 firewall"
echo "show interfaces ethernet eth1 firewall"
echo "show interfaces wireguard wg0 firewall"
echo ""

echo "=== VLAN Interface Firewall Assignments ==="
echo "show interfaces ethernet eth1 vif 2 firewall"
echo "show interfaces ethernet eth1 vif 3 firewall"
echo "show interfaces ethernet eth1 vif 4 firewall"
echo "show interfaces ethernet eth1 vif 6 firewall"
echo "show interfaces ethernet eth1 vif 10 firewall"
echo ""

echo "=== Firewall Statistics ==="
echo "show firewall statistics"
echo ""

echo "=== Recent Firewall Logs ==="
echo "show log firewall | tail -20"
echo ""

echo "=== Network Group Configuration ==="
echo "show firewall group"
echo ""

echo "=== Interface Status ==="
echo "show interfaces"
echo ""

echo "=== WireGuard Status ==="
echo "show interfaces wireguard"
echo ""

echo "=== NAT Rules ==="
echo "show nat statistics"
echo ""

echo "=== Port Forwarding Rules ==="
echo "show port-forward"
echo ""

echo "=== DHCP Status ==="
echo "show dhcp statistics"
echo ""

echo "=== DNS Configuration ==="
echo "show system name-server"
echo ""

echo "=== Current Configuration Summary ==="
echo "show configuration commands | grep firewall"
echo ""

echo "=== Security Test Commands ==="
echo "# Test from external (should be blocked):"
echo "# nmap -p 80,443,8080,5000,5001 [your-public-ip]"
echo ""
echo "# Test VPN connectivity:"
echo "# ping ***********"
echo "# curl -k https://***********13:5001"
echo ""
echo "# Test guest isolation (from guest device):"
echo "# ping *********** (should fail)"
echo "# ping ***********13 (should fail)"
echo "# ping ******* (should work)"
echo ""

echo "=== Expected Firewall Policies ==="
echo "GUEST_IN: Complete isolation - internet only"
echo "IOT_TRUSTED_IN: Home Assistant and Zigbee access"
echo "IOT_UNTRUSTED_IN: Internet only, no internal access"
echo "MEDIA_IN: DLNA optimized access"
echo "VPN_IN: Full access to all networks"
echo "WAN_IN: Block all except WireGuard (port 51820)"
echo "WAN_LOCAL: VPN and SSH only to router"
echo "WIFI_USERS_IN: Core services, blocked UniFi devices"
echo ""

echo "=== Interface Assignment Check ==="
echo "Expected assignments:"
echo "eth0 (WAN): WAN_IN (in), WAN_LOCAL (local)"
echo "eth1.2 (Guest): GUEST_IN (in)"
echo "eth1.3 (Media): MEDIA_IN (in)"
echo "eth1.4 (IoT Trusted): IOT_TRUSTED_IN (in)"
echo "eth1.6 (IoT Untrusted): IOT_UNTRUSTED_IN (in)"
echo "eth1.10 (WiFi Users): WIFI_USERS_IN (in)"
echo "wg0 (VPN): VPN_IN (in)"
echo ""
