#!/bin/bash

# Complete EdgeRouter Firewall Re-enablement Script
# This script ensures all firewall policies are properly configured and applied
# Network: VPN-only access with comprehensive VLAN security

echo "=== Complete EdgeRouter Firewall Re-enablement ==="
echo "Configuring comprehensive firewall policies for VPN-only access"
echo ""

echo "Network Configuration:"
echo "- Core Infrastructure: ***********/24 (VLAN 1)"
echo "- Guest Network: **********/24 (VLAN 2) - ISOLATED"
echo "- Media Network: ***********/24 (VLAN 3) - DLNA"
echo "- IoT Trusted: ***********/24 (VLAN 4)"
echo "- VPN Network: ***********/24 (VLAN 5)"
echo "- IoT Untrusted: ***********/24 (VLAN 6)"
echo "- WiFi Users: ************/24 (VLAN 10)"
echo ""

echo "=== CRITICAL: Backup Current Configuration First ==="
echo "Before proceeding, backup your current config:"
echo "show configuration commands > /tmp/backup-config.txt"
echo ""

echo "=== Step 1: Configure Network Groups ==="
echo "configure"
echo ""

echo "# Core network groups"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ***********/24"
echo "set firewall group network-group INTERNAL_NETWORKS network ************/24"
echo ""

echo "set firewall group network-group GUEST_NETWORK network **********/24"
echo "set firewall group network-group VPN_NETWORK network ***********/24"
echo "set firewall group network-group CORE_SERVICES network ***********/24"
echo ""

echo "# Service port groups"
echo "set firewall group port-group WEB_PORTS port 80"
echo "set firewall group port-group WEB_PORTS port 443"
echo "set firewall group port-group WEB_PORTS port 8080"
echo "set firewall group port-group WEB_PORTS port 8443"
echo "set firewall group port-group WEB_PORTS port 9080"
echo ""

echo "set firewall group port-group SYNOLOGY_PORTS port 5000"
echo "set firewall group port-group SYNOLOGY_PORTS port 5001"
echo "set firewall group port-group SYNOLOGY_PORTS port 5006"
echo "set firewall group port-group SYNOLOGY_PORTS port 5007"
echo ""

echo "=== Step 2: Configure WAN Firewall Policies ==="
echo ""

echo "# WAN_IN - Internet to Internal Network"
echo "set firewall name WAN_IN default-action drop"
echo "set firewall name WAN_IN description 'WAN to internal - VPN only'"
echo ""

echo "set firewall name WAN_IN rule 10 action accept"
echo "set firewall name WAN_IN rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_IN rule 10 destination port 51820"
echo "set firewall name WAN_IN rule 10 protocol udp"
echo ""

echo "set firewall name WAN_IN rule 20 action drop"
echo "set firewall name WAN_IN rule 20 description 'Block HTTP/HTTPS from WAN'"
echo "set firewall name WAN_IN rule 20 destination group port-group WEB_PORTS"
echo "set firewall name WAN_IN rule 20 protocol tcp"
echo "set firewall name WAN_IN rule 20 log enable"
echo ""

echo "set firewall name WAN_IN rule 30 action accept"
echo "set firewall name WAN_IN rule 30 description 'Allow established/related'"
echo "set firewall name WAN_IN rule 30 state established enable"
echo "set firewall name WAN_IN rule 30 state related enable"
echo ""

echo "# WAN_LOCAL - Internet to Router"
echo "set firewall name WAN_LOCAL default-action drop"
echo "set firewall name WAN_LOCAL description 'WAN to router - VPN and SSH only'"
echo ""

echo "set firewall name WAN_LOCAL rule 10 action accept"
echo "set firewall name WAN_LOCAL rule 10 description 'Allow WireGuard VPN'"
echo "set firewall name WAN_LOCAL rule 10 destination port 51820"
echo "set firewall name WAN_LOCAL rule 10 protocol udp"
echo ""

echo "set firewall name WAN_LOCAL rule 20 action accept"
echo "set firewall name WAN_LOCAL rule 20 description 'Allow SSH (optional)'"
echo "set firewall name WAN_LOCAL rule 20 destination port 22"
echo "set firewall name WAN_LOCAL rule 20 protocol tcp"
echo ""

echo "set firewall name WAN_LOCAL rule 30 action accept"
echo "set firewall name WAN_LOCAL rule 30 description 'Allow established/related'"
echo "set firewall name WAN_LOCAL rule 30 state established enable"
echo "set firewall name WAN_LOCAL rule 30 state related enable"
echo ""

echo "=== Step 3: Configure Guest Network Isolation ==="
echo ""

echo "# GUEST_IN - Complete isolation"
echo "set firewall name GUEST_IN default-action drop"
echo "set firewall name GUEST_IN description 'Guest network complete isolation'"
echo ""

echo "set firewall name GUEST_IN rule 10 action drop"
echo "set firewall name GUEST_IN rule 10 description 'Block access to internal networks'"
echo "set firewall name GUEST_IN rule 10 destination group network-group INTERNAL_NETWORKS"
echo "set firewall name GUEST_IN rule 10 log enable"
echo ""

echo "set firewall name GUEST_IN rule 20 action drop"
echo "set firewall name GUEST_IN rule 20 description 'Block access to router'"
echo "set firewall name GUEST_IN rule 20 destination address ***********"
echo "set firewall name GUEST_IN rule 20 log enable"
echo ""

echo "set firewall name GUEST_IN rule 30 action accept"
echo "set firewall name GUEST_IN rule 30 description 'Allow established/related'"
echo "set firewall name GUEST_IN rule 30 state established enable"
echo "set firewall name GUEST_IN rule 30 state related enable"
echo ""

echo "=== Step 4: Configure VPN Network Access ==="
echo ""

echo "# VPN_IN - Full access"
echo "set firewall name VPN_IN default-action accept"
echo "set firewall name VPN_IN description 'VPN network - full access'"
echo ""

echo "=== Step 5: Apply Firewall Policies to Interfaces ==="
echo ""

echo "# Apply WAN firewall policies"
echo "set interfaces ethernet eth0 firewall in name WAN_IN"
echo "set interfaces ethernet eth0 firewall local name WAN_LOCAL"
echo ""

echo "# Apply VLAN firewall policies"
echo "set interfaces ethernet eth1 vif 2 firewall in name GUEST_IN"
echo ""

echo "# Apply VPN firewall policy"
echo "set interfaces wireguard wg0 firewall in name VPN_IN"
echo ""

echo "=== Step 6: Commit and Save ==="
echo "commit"
echo "save"
echo "exit"
echo ""

echo "=== Verification Commands ==="
echo "show firewall statistics"
echo "show interfaces"
echo "show log firewall | tail -20"
echo ""

echo "=== Test Commands ==="
echo "# From VPN client - should work:"
echo "curl -k https://nas.mdewaele.freeddns.org"
echo ""
echo "# From internet - should fail:"
echo "nmap -p 80,443,8080 [your-public-ip]"
echo ""
